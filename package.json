{"name": "kefu-web", "version": "0.0.0", "license": "ISC", "packageManager": "pnpm@8.6.3", "scripts": {"cli": "tsx bin/index.ts", "dev": "pnpm cli dev", "build:test": "turbo run build:test && npm run copy", "build:stage": "turbo run build:stage && npm run copy", "build:master": "turbo run build:master && npm run copy", "copy": "tsx bin/BuildApp/copy.ts", "serve": "vite preview --mode prod", "prepare": "husky install", "cz": "git add . && git cz", "createApp": "pnpm cli tmp"}, "lint-staged": {"*.{vue,js,ts}": "eslint --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@guanghe-pub/nexus-axios": "^2.0.5", "@guanghe-pub/nexus-components": "^1.0.20", "@guanghe-pub/nexus-tsconfig": "^1.0.8", "@guanghe-pub/nexus-utils": "^1.0.8", "@guanghe-pub/onion-oss-vite-plugin": "^0.0.3", "@guanghe-pub/onion-ui": "^2.3.8", "@guanghe-pub/onion-utils": "2.11.0", "@guanghe-pub/postcss-plugin-auto-webp": "0.1.0-beta.1", "@guanghe-pub/vite-plugin-import-onion-ui": "^1.1.4", "@guanghe-pub/yc-upload": "^0.2.1", "@unocss/reset": "^0.53.4", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.7.2", "axios": "^1.4.0", "axios-jsonp": "^1.0.4", "axios-retry": "^3.5.1", "dayjs": "^1.11.9", "element-plus": "^2.8.2", "eruda": "^3.0.1", "exif-js": "^2.3.0", "fs-extra": "^11.1.1", "normalize.css": "link:@unocss/reset/normalize.css", "paho-mqtt": "^1.1.0", "pinia": "2.1.4", "pinia-plugin-persistedstate": "^3.1.0", "postcss": "^8.4.24", "qs": "^6.11.2", "vant": "^4.6.2", "viewerjs": "^1.11.6", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.14", "vue-router": "^4.2.2"}, "devDependencies": {"@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@constq/eslint-config-qzr": "^0.0.5", "@constq/qzr-utils": "^1.2.5", "@iconify-json/ion": "^1.1.11", "@rollup/plugin-typescript": "^11.1.6", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.2", "@types/mockjs": "^1.0.7", "@types/node": "^20.3.3", "@types/paho-mqtt": "^1.0.7", "@types/qs": "^6.9.7", "@types/rollup-plugin-visualizer": "^4.2.1", "@types/vue": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "@unoCSS/preset-icons": "npm:@unocss/preset-icons@^0.53.6", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/compiler-sfc": "^3.3.4", "@vue/test-utils": "^2.4.0", "cac": "^6.7.14", "chalk": "4.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.44.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "eslint-plugin-vue": "^9.15.1", "execa": "^8.0.1", "husky": "^8.0.3", "inquirer": "^9.2.12", "inquirer-autocomplete-prompt": "^3.0.1", "jest": "^29.6.0", "kolorist": "^1.8.0", "lint-staged": "^13.2.3", "minimist": "^1.2.8", "postcss-px-to-viewport-8-plugin": "^1.2.5", "rollup": "^4.9.6", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.63.6", "stylus": "^0.59.0", "ts-jest": "^29.1.1", "tsx": "^4.7.0", "turbo": "^2.0.9", "typescript": "^5.1.6", "unocss": "^0.53.4", "unplugin-auto-import": "^0.16.5", "unplugin-vue-components": "^0.25.1", "vite": "^4.3.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-html-config": "^1.0.11", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-global-api": "^0.4.1", "vue-tsc": "^1.8.27"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}