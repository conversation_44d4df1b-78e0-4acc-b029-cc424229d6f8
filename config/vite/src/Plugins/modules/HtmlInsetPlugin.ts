function insertHtmlPlugin(options = {}) {
  const { targetId, htmlContent } = options as any

  return {
    name: 'vite-plugin-insert-html',
    transformIndexHtml(html) {
      if (!targetId || !htmlContent) {
        throw new Error('Both targetId and htmlContent must be provided.')
      }

      // 使用正则找到目标 id 的元素，并插入 HTML 内容
      const targetElementRegExp = new RegExp(
        `<[^>]*id="${targetId}"[^>]*>`,
        'i'
      )

      if (targetElementRegExp.test(html)) {
        // 在目标元素后插入 HTML 内容
        return html.replace(targetElementRegExp, (match) => {
          return `${match}${htmlContent}`
        })
      }

      return html
    },
  }
}
export default function (plugins, env) {
  plugins.push(insertHtmlPlugin({
    targetId: 'app', // 目标元素的 ID
    htmlContent: `
    <div class="oi-loading">
      <div class="oi-loading__wrap">
        <div class="oi-loading__cell oi-loading__egg1"></div>
        <div class="oi-loading__cell oi-loading__egg2"></div>
        <div class="oi-loading__cell oi-loading__egg3"></div>
        <div class="oi-loading__cell oi-loading__egg4"></div>
        <div class="oi-loading__cell oi-loading__egg5"></div>
        <div class="oi-loading__cell oi-loading__egg6"></div>
        <div class="oi-loading__cell oi-loading__egg7"></div>
        <div class="oi-loading__walk">
          <div class="oi-loading__people">
            <img class="oi-loading__hammer" src="https://fp.yangcong345.com/middle/loading/hammer.svg" alt="" />
            <img class="oi-loading__body" src="https://fp.yangcong345.com/middle/loading/body.svg" alt="" />
            <img class="oi-loading__cap" src="https://fp.yangcong345.com/middle/loading/cap.svg" alt="" />
            <img class="oi-loading__head" src="https://fp.yangcong345.com/middle/loading/head.svg" alt="" />
            <img class="oi-loading__arm" src="https://fp.yangcong345.com/middle/loading/arms.svg" alt="" />
          </div>
        </div>
      </div>
    </div>
    `, // 要插入的 HTML 代码
  }))
}