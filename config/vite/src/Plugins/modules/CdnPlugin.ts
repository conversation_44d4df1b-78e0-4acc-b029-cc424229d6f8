import { Plugin as importToCDN } from 'vite-plugin-cdn-import'

export default function(plugins) {
  const config = {
    modules: [
      {
        name: 'axios',
        var: 'axios',
        path: '//fp.yangcong345.com/middle/0.19.2/axios.min.js',
      },
      // {
      //   name: 'yc-webviewbridge',
      //   var: 'YCWebViewBridge',
      //   path: '//fp.yangcong345.com/middle/0.6.0/YCWebViewBridge.js',
      // },
      // {
      //   name: '@guanghe-pub/onion-utils',
      //   var: 'onionUtils',
      //   path: '//fp.yangcong345.com/middle/2.11.0/onion-utils.min.js',
      // },
      // {
      //   name: '@guanghe-pub/web-track',
      //   var: 'OnionWebTrack',
      //   path: '//fp.yangcong345.com/web-track-1.1.1/webTrack.js',
      // },
      // {
      //   name: '@guanghe-pub/web-monitor',
      //   var: 'OnionWebMonitor',
      //   path: '//fp.yangcong345.com/fe-monitor-1.2.5/monitor.js',
      // },
      // {
      //   name: 'dayjs',
      //   var: 'dayjs',
      //   path: '//fp.yangcong345.com/middle/1.8.29/dayjs.min.js',
      // },
    ]
  }

  plugins.push(importToCDN(config))
}
