#!/usr/bin/env node

/**
 * @Date         : 2024-01-19 13:52:45
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import fse from 'fs-extra'
import path from 'path'
import { yellow } from 'kolorist'

export default async function(dir) {
  const tmpUrl = path.join(process.cwd(), './bin/CreateApp/Template/vue3')
  const targetUrl = path.join(process.cwd(), `./apps/${dir}`)
  await fse.copy(tmpUrl, targetUrl)
  console.log(yellow('app拷贝完成'))
}
