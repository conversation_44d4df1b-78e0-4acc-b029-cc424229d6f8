/**
 * @Date         : 2023-11-02 12:28:09
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import fse from 'fs-extra'
import path from 'path'
import chalk from 'chalk'

let appRoot = path.join(process.cwd(), `./apps`)
let buildDir = path.join(process.cwd(), `./dist`)

function copy() {
  let dirs = fse.readdirSync(appRoot)
  dirs.forEach((dir) => {
    let pathname = path.join(appRoot, dir)
    let packageName = path.join(pathname, 'package.json')
    if (fse.statSync(pathname).isDirectory() && fse.existsSync(packageName)) {
      fse.copySync(path.join(pathname, 'dist'), path.join(buildDir, dir))
    }
  })
}

function removeBuild() {
  return fse.remove(buildDir)
}
async function run() {
  await removeBuild()
  copy()
  console.log(chalk.green('app拷贝完成'))
}
run()

