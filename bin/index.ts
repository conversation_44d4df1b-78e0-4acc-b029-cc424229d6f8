#!/usr/bin/env node

/**
 * @Date         : 2024-01-19 13:49:16
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
*/

import minimist from 'minimist'
import cac from 'cac'
import { yellow } from 'kolorist'

import createApp from './CreateApp/index.js'
import dev from './Dev/index.js'

const cli = cac('lite')

// 生成app
cli
  .command('tmp [projectName]', `生成前端项目模板`)
  .option('-n, --name', '生成app名')
  .action(async (dir, options) => {
    console.log(yellow('-n 生成app名'))
    const argv = minimist(process.argv.slice(2), { string: ['_'] })
    await createApp(dir)
  })

cli
  .command('dev', `启动app`)
  .action(async (dir, options) => {
    dev()
  })

cli.help()

cli.parse()
