/**
 * @Date         : 2024-01-19 15:16:25
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import path from 'path'
import inquirer from 'inquirer'
import inquirerAutocompletePrompt from 'inquirer-autocomplete-prompt'
import fs from 'fs'
import fse from 'fs-extra'
import { execaCommand } from 'execa'

inquirer.registerPrompt('autocomplete', inquirerAutocompletePrompt)

// 获取指定文件夹下的所有文件路径
function getFiles(dirPath): Promise<{
  name: string
  value: string
}[]> {
  return new Promise((resolve, reject) => {
    fse.readdir(dirPath, (err, files) => {
      // 读取 apps 文件夹下的文件列表
      if (err) {
        console.error(err)
        reject(err)
        return
      }
      const folderNames = files
        .map((file) => path.join(dirPath, file)) // 拼接路径，生成文件路径数组
        .filter((filePath) => fs.statSync(filePath).isDirectory()) // 筛选出文件夹路径数组
        .map((i) => ({ name: path.basename(i), value: i })) // 转换为文件夹名称和完整路径组成的对象数组
      resolve(folderNames)
    })
  })
}

export default async function () {
  const appsUrl = path.join(process.cwd(), `./apps`)
  const files = await getFiles(appsUrl)
  const { selectedFile } = await inquirer.prompt([
    {
      type: 'autocomplete',
      name: 'selectedFile',
      message: '请选择要启动的服务:',
      source: async (answersSoFar, input) => {
        const filteredFiles = input ? files.filter((file) => file.name.includes(input)) : files
        return filteredFiles
      },
      pageSize: 10
    }
  ])
  try {
    const bootstrap = execaCommand(`pnpm dev`, {
      cwd: selectedFile,
      shell: true,
      stdio: 'inherit'
    })
    const { stdout } = await bootstrap
    return stdout
  } catch (error) {
    console.error(error)
    process.exit(1)
  }
}
