/**
 * @Date         : 2021-04-21 11:18:06
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2024-06-19 15:45:12
 */

module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'plugin:vue/vue3-essential',
    'plugin:vue/vue3-strongly-recommended',
    '@constq/eslint-config-qzr/index',
    '@constq/eslint-config-qzr/vue',
    '@constq/eslint-config-qzr/typescript',
    'plugin:jest/recommended',
    'vue-global-api'
  ],
  globals: {
    defineProps: true,
    defineEmits: true,
    Paho: true,
    ElMessage: true,
    ElMessageBox: true,
  },
  parserOptions: {
    ecmaVersion: 12,
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
  },
  ignorePatterns: ['index.html', '/docker'],
  plugins: [
    'vue',
    '@typescript-eslint',
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    'complexity': 'off',
    'vue/no-v-html': 'off',
    'vue/require-prop-types': 'off',
    'vue/no-v-text-v-html-on-component': 'off'
  },
}
