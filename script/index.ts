import { getQueryObject } from '@guanghe-pub/onion-utils'

export function copyStr(text) {
  const domInput = document.createElement('input')
  domInput.value = text
  document.body.appendChild(domInput)  // 添加input节点
  domInput.select() // 选择对象;
  document.execCommand('Copy') // 执行浏览器复制命令
  domInput.remove()
}

export function checkApiSix() {
  const { apiSix } = getQueryObject()
  if (apiSix === '1') {
    return import.meta.env.VITE_HOST_APISIX
  } else {
    return import.meta.env.VITE_HOST_API
  }
}
