/*
 * @Date         : 2023-11-14 10:43:30
 * @Description  : 全局链接跳转方法
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-28 10:25:54
 */


export default function (url: string, type: '_self' | '_blank' = '_blank', replace: boolean = false) {
  const search = location.search

  if (url.includes('?')) {  // 拼接参数
    url = `${url}&${search.slice(1)}`
  } else {
    url = `${url}${search}`
  }

  if (replace) {
    location.replace(url)
  } else {
    open(url, type)
  }
}
