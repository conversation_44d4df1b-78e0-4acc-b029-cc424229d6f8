server {
    listen   80; ## listen for ipv4; this line is default and implied
    listen   [::]:80 default ipv6only=on; ## listen for ipv6

    root /var/www/html;
    index index.html;

    server_tokens  off; # disable the Server nginx header

    server_name _; # all hostnames

    # enable gzip
    gzip on;
    gzip_disable "msie6";

    gzip_comp_level 6;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/rss+xml
        image/svg+xml;

    location ~ ^/kefu-web-pc$ {
        rewrite ^ /kefu-web-pc/pc/index.html last;
    }

    location ~ ^/kefu-web-pc/([^/]+) {
        set $project $1;
        try_files $uri $uri/ /kefu-web-pc/$project/index.html;
    }

}
