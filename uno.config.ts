import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(), // UnoCSS 的默认样式预设
    presetAttributify(), // 属性化模式支持 增加可读性
    presetIcons()
  ],
  rules: [
    ['flexD', {
      display: 'flex',
      'justify-content': 'center',
      'align-items': 'center',
    }],
    [
      'linkStyle', {
        color: '#576B95',
        text: '14px'
      }
    ],
    [
      'shadowStyle', {
        'box-shadow': '0px 0px 12px rgba(0, 0, 0, 0.12);'
      }
    ],
    [/^fs-(\d+\.{0,1}\d{0,2})$/, ([, d]) => ({ 'font-size': `${d}px` })],
  ]
})
