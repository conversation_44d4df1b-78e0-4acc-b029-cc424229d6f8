import { defineConfig } from 'rollup'
import typescript from '@rollup/plugin-typescript' // typescript插件
import dts from 'rollup-plugin-dts'

export default defineConfig([{
  input: 'src/index.ts',
  output: {
    file: 'dist/index.js',
    format: 'es', // ES模块文件
    exports: 'auto'
  },
  plugins: [
    typescript(),
  ]
}, {
  input: 'src/index.ts',
  output: [{ file: 'dist/index.d.ts', format: 'es' }],
  plugins: [dts()]
}])
