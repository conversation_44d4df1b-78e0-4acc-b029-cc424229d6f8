import { isInNative } from '@guanghe-pub/onion-utils'

const map = [{
  path: '/problem-app',
  newHost: 'xxgj.yangcongxing.com',
}, {
  path: '/study-app',
  newHost: 'xxgj.yangcongxing.com',
}, {
  path: '/onion-learning',
  newHost: 'xxgj.yangcongxing.com',
}, {
  path: '/middle-resident',
  newHost: 'xxgj.yangcongxing.com',
}, {
  path: '/tenon',
  newHost: 'sccl.yangcongxing.com',
}, {
  path: '/lsy',
  newHost: 'kf.yangcongxing.com'
}, {
  path: '/kefu-web-pc',
  newHost: 'kf.yangcongxing.com'
}, {
  oldHost: 'telec.yangcong345.com',
  newHost: 'dx.yangcongxing.com'
}, {
  oldHost: 'devices-service.yangcong345.com',
  newHost: 'jszc.yangcongxing.com'
}]

/**
 * 根据环境获取对应的host
 * @param {string} host - 基础host
 * @param {string} environment - 环境类型 ('test' | 'stage' | 'prod')
 * @returns {string} 对应环境的host
 */
function getEnvironmentHost(host: string, environment: string): string {
  if (environment === 'test') {
    return host.replace(/^([^.]+)\./, '$1-test.')
  }
  if (environment === 'stage') {
    return host.replace(/^([^.]+)\./, '$1-stage.')
  }
  return host // 生产环境保持原样
}

/**
 * 检测URL中的环境类型
 * @param {string} url - 输入的URL字符串
 * @returns {string} 环境类型 ('test' | 'stage' | 'prod')
 */
function detectEnvironment(url: string): string {
  if (url.includes('-test.')) {
    return 'test'
  }
  if (url.includes('-stage.')) {
    return 'stage'
  }
  return 'prod'
}

/**
 * 替换单个URL的host
 * @param {string} url - 单个URL字符串
 * @returns {string} 替换后的URL字符串
 */
function replaceSingleUrlHost(url: string): string {
  try {
    const urlObj = new URL(url)
    const currentHost = urlObj.hostname
    const currentPath = urlObj.pathname
    const environment = detectEnvironment(url)

    // 优先匹配oldHost规则
    for (const item of map) {
      if (item.oldHost) {
        // 获取当前环境下的oldHost
        const envOldHost = getEnvironmentHost(item.oldHost, environment)

        if (currentHost === envOldHost) {
          // 替换为对应环境的newHost
          const envNewHost = getEnvironmentHost(item.newHost, environment)
          urlObj.hostname = envNewHost
          return urlObj.toString()
        }
      }
    }

    // 如果没有匹配到oldHost，则匹配path规则
    for (const item of map) {
      if (item.path && currentPath.startsWith(item.path)) {
        // 替换为对应环境的newHost
        const envNewHost = getEnvironmentHost(item.newHost, environment)
        urlObj.hostname = envNewHost
        return urlObj.toString()
      }
    }

    // 如果都没有匹配到，但是是yangcong345.com域名，尝试通过path匹配
    if (currentHost.includes('yangcong345.com')) {
      for (const item of map) {
        if (item.path && currentPath.startsWith(item.path)) {
          const envNewHost = getEnvironmentHost(item.newHost, environment)
          urlObj.hostname = envNewHost
          return urlObj.toString()
        }
      }
    }

    // 如果都没有匹配到，返回原URL
    return url
  } catch (error) {
    // 如果URL格式不正确，返回原字符串
    return url
  }
}

/**
 * 替换HTML字符串中的所有URL
 * @param {string} htmlString - 包含URL的HTML字符串
 * @returns {string} 替换后的HTML字符串
 */
function replaceUrlsInHtml(htmlString: string): string {
  let result = htmlString

  // 1. 处理有href属性的a标签 - 修复正则表达式以支持嵌套HTML标签
  const linkRegex = /<a\s+([^>]*?)href=["']([^"']+)["']([^>]*?)>(.*?)<\/a>/gi
  result = result.replace(linkRegex, (match, beforeHref, href, afterHref, textContent) => {
    // 替换href属性中的URL
    const replacedHref = replaceSingleUrlHost(href)

    // 如果文本内容与原href相同，也替换文本内容
    let replacedTextContent = textContent
    if (textContent.trim() === href) {
      replacedTextContent = replacedHref
    }

    return `<a ${beforeHref}href="${replacedHref}"${afterHref}>${replacedTextContent}</a>`
  })

  // 2. 处理没有href属性但包含onclick的a标签
  const onclickLinkRegex = /<a\s+([^>]*?)>(.*?)<\/a>/gi
  result = result.replace(onclickLinkRegex, (match, attributes, textContent) => {
    let newAttributes = attributes
    let newTextContent = textContent

    // 检查是否包含browserJump的onclick属性
    const browserJumpMatch = attributes.match(/onclick=["']browserJump\(([^)]+)\)["']/i)
    if (browserJumpMatch) {
      const browserJumpParams = browserJumpMatch[1]

      // 替换browserJump参数中的URL
      const urlRegex = /(&quot;url&quot;:&quot;)(https?:\/\/[^&"]+)/g
      const updatedParams = browserJumpParams.replace(urlRegex, (urlMatch, prefix, url) => {
        const replacedUrl = replaceSingleUrlHost(url)
        return prefix + replacedUrl
      })

      // 更新onclick属性
      newAttributes = attributes.replace(browserJumpParams, updatedParams)
    }

    // 检查文本内容是否为URL并替换
    const urlMatch = textContent.trim().match(/^https?:\/\/[^\s]+$/)
    if (urlMatch) {
      const originalUrl = urlMatch[0]
      const replacedUrl = replaceSingleUrlHost(originalUrl)
      newTextContent = textContent.replace(originalUrl, replacedUrl)
    }

    return `<a ${newAttributes}>${newTextContent}</a>`
  })

  return result
}

/**
 * 根据map配置替换HTML字符串中的所有URL或单个URL
 * @param {string} input - 包含URL的HTML字符串或单个URL字符串
 * @returns {string} 替换后的字符串
 */
export function replaceUrlHost(input: string): string {
  if (!input) {
    return input
  }

  if (isInNative()) {
    return input
  }

  // 判断是否为单个URL（以http://或https://开头且不包含HTML标签）
  const isUrl = /^https?:\/\//.test(input.trim()) && !/<[^>]+>/.test(input)

  if (isUrl) {
    // 处理单个URL
    return replaceSingleUrlHost(input)
  } else {
    // 处理HTML字符串
    return replaceUrlsInHtml(input)
  }
}