/*
 * @Date         : 2023-11-14 10:43:30
 * @Description  : 全局链接跳转方法
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2024-01-25 14:25:56
 */


/**
 * @description: 全局链接跳转方法 自动拼接当前url携带的参数
 * @param {string} url
 * @param {'_self' | '_blank'} type
 * @param {boolean} replace
 */
export function openUrl(url: string, type: '_self' | '_blank' = '_blank', replace: boolean = false) {
  const search = location.search

  if (url.includes('?')) {  // 拼接参数
    url = `${url}&${search.slice(1)}`
  } else {
    url = `${url}${search}`
  }

  if (replace) {
    location.replace(url)
  } else {
    open(url, type)
  }
}
