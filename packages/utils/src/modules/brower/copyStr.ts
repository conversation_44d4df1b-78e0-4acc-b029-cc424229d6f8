/*
 * @Date         : 2024-01-25 14:16:39
 * @Description  : 拷贝字符串
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

/**
 * @description: 拷贝字符串到剪切板
 * @param {*} text
 */
export function copyStr(text) {
  const domInput = document.createElement('input')
  domInput.value = text
  document.body.appendChild(domInput)  // 添加input节点
  domInput.select() // 选择对象;
  document.execCommand('Copy') // 执行浏览器复制命令
  domInput.remove()
}
