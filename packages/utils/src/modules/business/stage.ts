/*
 * @Date         : 2024-01-25 14:16:39
 * @Description  : 判断用户学段
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

export const schoolYearConfig = {
  // 小学
  primary: ['一年级', '二年级', '三年级', '四年级', '五年级'],
  // 初中
  middle: ['七年级', '八年级', '九年级'],
  // 高中
  high: ['高一', '高二', '高三'],
  // 中职
  middleVocational: ['职一', '职二', '职三'],

  isPrimary(schoolYear: string) {
    return schoolYearConfig.primary.includes(schoolYear)
  },
  isMiddle(schoolYear: string) {
    return schoolYearConfig.middle.includes(schoolYear)
  },
  isHigh(schoolYear: string) {
    return schoolYearConfig.high.includes(schoolYear)
  },
  isMiddleVocational(schoolYear: string) {
    return schoolYearConfig.middleVocational.includes(schoolYear)
  }
}

export const stageMap = {
  none: { value: 0, label: '无', key: 'none' },
  primary: { value: 1, label: '小学', key: 'primary' },
  middle: { value: 2, label: '初中', key: 'middle' },
  high: { value: 3, label: '高中', key: 'high' },
  middleVocational: { value: 4, label: '中职', key: 'middleVocational' },
}

/**
 * @description: 判断学段
 * @param {string} schoolYear
 * @param {string} schoolYear54or63
 *   none: { value: 0, label: '无', key: 'none' },
      primary: { value: 1, label: '小学', key: 'primary' },
      middle: { value: 2, label: '初中', key: 'middle' },
      high: { value: 3, label: '高中', key: 'high' },
      middleVocational: { value: 4, label: '中职', key: 'middleVocational' },
 */
export function schoolYearToStage(schoolYear: string, schoolYear54or63: string = ''):{
  value: number
  label: string
  key: string
} {
  if (schoolYearConfig.isPrimary(schoolYear)) {
    return stageMap.primary
  }
  if (schoolYearConfig.isMiddle(schoolYear)) {
    return stageMap.middle
  }
  if (schoolYearConfig.isHigh(schoolYear)) {
    return stageMap.high
  }
  if (schoolYearConfig.isMiddleVocational(schoolYear)) {
    return stageMap.middleVocational
  }

  if (schoolYear === '六年级') {
    return schoolYear54or63 === 'fiveFour' ? stageMap.middle : stageMap.primary
  }

  return stageMap.none
}
