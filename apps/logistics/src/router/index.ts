import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

// import PC from '@/views/PC/index.vue'
// import Mobile from '@/views/Mobile/index.vue'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Logistics',
    component: () => import('@/views/Logistics/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('@/views/Logistics/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/Logistics',
    name: 'Logistics',
    component: () => import('@/views/Logistics/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/Detail',
    name: 'Detail',
    component: () => import('@/views/Detail/index.vue'),
    meta: {
      keepAlive: false
    }
  },
]

const router = createRouter({
  history: createWebHistory('kefu-web-pc/logistics'),
  routes,
})

router.beforeEach(beforeEach)

export default router
