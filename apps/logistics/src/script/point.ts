import { BuryPoint, getQueryObject } from '@guanghe-pub/onion-utils'
import axios from 'axios'
/**
 * https://guanghe.feishu.cn/sheets/shtcnTP5rhn8AJIpuQwCLJPACIc?sheet=b7c3e3
 */
const points = {
  getLogisticsInquiryH5: {
    category: 'activity',
    data: ['authorizationStatus'],
    desc: '洋葱物流H5查询页曝光 当前是否授权登录[1:是-2:否]',
  },
  clickLogisticsInquiryH5Button: {
    category: 'activity',
    data: ['authorizationStatus', 'button'],
    desc: '洋葱物流H5查询页按钮点击 点击按钮【查询、点击催一催、物流进度查询】',
  },
  clickLogisticsProgressPopupButton: {
    category: 'activity',
    data: ['authorizationStatus', 'button'],
    desc: '洋葱物流H5查询页物流进度弹窗点击 点击按钮【立即跳转，自动跳转】',
  },
  getLogisticsDetailsPageH5: {
    category: 'activity',
    data: ['authorizationStatus'],
    desc: '洋葱物流H5明细页曝光'
  }
}

export default new BuryPoint(points, {
  contextData: {
    authorizationStatus: getQueryObject().userId ? 1 : 2
  },
  env: import.meta.env.VITE_GETHOST,
  axios
})
