import Axios from './axios'

interface List<T> {
  items: T[]
  total: number
}

export enum OrderStatus {
  '待发货' = 1,
  '已发货' = 2,
}

export interface Order {
  expressName: string
  expressNumber: string
  createdAt: string
  status: 1 | 2 // 状态 1 为待发货 2 为已发货
  goods: string // 商品名称
}

/**
 * @description: 物流列表查询
 */
export function searchOrder(par: {
  userId?: string
  mobile?: string
  pages: number
  pageSize: number
}): Promise<List<Order>> {
  return Axios.post('/web/logistics', par)
}

export interface Logistics {
  context: string
  time: string
  ftime: string
  status: string
}
/**
 * @description: 物流进度查询
 * @url /web/logistics
 */
export function searchLogistics(par: {
  expressName: string // 物流公司
  expressNumber: string // 无物流单号
  recipientPhoneNum: string // 收货人手机号
}): Promise<{
  list: Logistics[]
}> {
  return Axios.get('/web/logistics', par)
}
