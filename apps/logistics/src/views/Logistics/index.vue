<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  : 物流查询
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-12-01 15:44:44
-->

<template>
  <div class="content-container flex items-center flex-col">
    <!-- <Search v-if="!orderStore.userId"
            class="w-100%" /> -->

    <div v-if="orderStore.orderList.length === 0"
         class="img-box flex-1">
      <img v-if="orderStore.unSearch"
           src="@/assets/main_order.png">
      <img v-else
           src="@/assets/no_order.png">
    </div>

    <OrderList v-else />
  </div>
</template>

<script lang='ts' setup>
import Search from './children/Search.vue'
import { getQueryObject } from '@guanghe-pub/onion-utils'
import { useOrder } from '@/store/order'
import OrderList from './children/OrderList.vue'
import point from '@/script/point'

const { userId } = getQueryObject()
const orderStore = useOrder()
orderStore.userId = userId


onMounted(() => {
  orderStore.reset()

  if (userId) {
    orderStore.onLoad()
  }
  point.h5Post('getLogisticsInquiryH5')
})
</script>

<style scoped lang='scss'>
.content-container {
  background: #F2F9FE;
  min-height: 100vh;
}

.img-box {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 383px;
    height: 305px;
  }
}
</style>
