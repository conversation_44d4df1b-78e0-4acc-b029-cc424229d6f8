<!--
 * @Date         : 2023-11-10 15:06:20
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-14 15:29:26
-->

<template>
  <div class="search-container">
    <van-search v-model="searchVal"
                clearable
                show-action
                maxlength="11"
                placeholder="请输入绑定洋葱ID的手机号">
      <template #action>
        <van-button round
                    :loading="orderStore.loading"
                    class="w-130px h-64px!"
                    type="primary"
                    @click="search">搜索</van-button>
      </template>
    </van-search>
  </div>
</template>

<script lang='ts' setup>
import { useOrder } from '@/store/order'
import { showFailToast } from 'vant'
import 'vant/es/toast/style'
import point from '@/script/point'

const orderStore = useOrder()
const searchVal = ref('')

function search() {
  const reg = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/
  if (!reg.test(searchVal.value)) {
    showFailToast('请输入正确的手机号')
    return
  }

  orderStore.reset()
  orderStore.phone = searchVal.value
  orderStore.onLoad()

  point.h5Post('clickLogisticsInquiryH5Button', {
    button: '搜索'
  })
}

</script>

<style scoped lang='scss'>
.search-container {
  :deep(.van-search__content) {
    border-radius: 36px;
  }
  :deep(.van-icon-clear) {
    z-index: 9999;
  }
}
</style>
