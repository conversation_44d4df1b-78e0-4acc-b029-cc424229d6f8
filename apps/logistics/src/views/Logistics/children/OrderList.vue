<!--
 * @Date         : 2023-11-10 15:37:34
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-30 17:43:39
-->

<template>
  <div class="mt-30px">
    <van-list v-model:loading="orderStore.loading"
              :finished="orderStore.finished"
              finished-text="没有更多了"
              @load="orderStore.onLoad">
      <div v-for="(order, index) in orderStore.orderList"
           :key="index"
           class="mb-30px">
        <div class="w-690px bg-white rounded-16px">
          <div class="px-40px py-30px info-box text-28px">
            <div class="c-#1885FF flex items-center justify-start">
              <img src="@/assets/order_icon.png"
                   class="w-22px h-27px mr-9px">
              <span class="c-#1885FF!">物流单号：</span>
              <div class="c-#1885FF!">{{ order.expressNumber === '' ? '暂无' : order.expressNumber }}</div>
            </div>

            <div class="flex items-start justify-start">
              <span>商品名称：</span>
              <div>
                <div v-for="(item, idx) of order.goods.split('-')"
                     :key="idx">{{ item }}</div>
              </div>
            </div>

            <div>
              <span>发货状态：</span>
              <span :class="order.status === 1 ? 'c-#FC6443!' : 'c-#38D13E!'">{{ OrderStatus[order.status] }}</span>
            </div>

            <div><span>物流公司：</span>{{ order.expressName }}</div>

            <div><span>下单时间：</span>{{ order.createdAt }}</div>
          </div>

          <van-divider class="m-0!" />

          <div class="flexD h-104px">
            <van-button v-if="order.status === 1"
                        type="primary"
                        class="h-56px! w-362px bg-#1885FF! rounded-28px!"
                        @click="remind">点击催一催</van-button>
            <van-button v-else
                        type="primary"
                        class="h-56px! w-362px bg-#1885FF! rounded-28px!"
                        @click="inquire(order)">物流进度查询</van-button>
          </div>
        </div>
      </div>
    </van-list>
  </div>

  <RemindSheet v-model:visible="remindVisible" />
</template>

<script lang='ts' setup>
import { useOrder } from '@/store/order'
import { OrderStatus } from '@/api'
import RemindSheet from '../components/RemindSheet.vue'
import point from '@/script/point'
import { useRouter } from 'vue-router'

const router = useRouter()

const remindVisible = ref(false)
function remind() {
  point.h5Post('clickLogisticsInquiryH5Button', {
    button: '点击催一催'
  })
  remindVisible.value = true
}

function inquire(order) {

  console.log('order', order)
  router.push({
    name: 'Detail',
    query: { order: JSON.stringify(order) }
  })

  point.h5Post('clickLogisticsInquiryH5Button', {
    button: '物流进度查询'
  })
}

const orderStore = useOrder()
</script>

<style scoped lang='scss'>
.info-box {
  line-height: 44px;
  span {
    color: #999;
  }
  div {
    color: #222;
  }
}
</style>
