<!--
 * @Date         : 2023-11-10 16:51:44
 * @Description  : 提醒发货成功弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-10 17:02:51
-->

<template>
  <van-action-sheet v-model:show="_visible"
                    title=" ">
    <div class="flex items-center justify-evenly flex-col h-333px">
      <img src="@/assets/success.png"
           class="w-83px h-83px">
      <div class="c-#222 text-28px">提醒发货成功</div>
      <div class="c-#999 text-26px">已提醒仓库发货，您的包裹正在快马加鞭的赶</div>
      <van-button plain
                  class="rounded-28px! w-256px h-56px!"
                  type="primary"
                  @click="_visible = false">我知道了</van-button>
    </div>
  </van-action-sheet>
</template>

<script lang='ts' setup>
import { useModel } from '@/hooks/useBasic'

const props = defineProps<{
  visible: boolean
}>()
const emits = defineEmits(['update:visible'])
const _visible = useModel(props, emits, 'visible')

</script>

<style scoped lang='scss'>

</style>
