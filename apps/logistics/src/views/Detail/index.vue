<!--
 * @Date         : 2023-11-30 11:30:56
 * @Description  : 详情
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-12-01 13:49:24
-->

<template>
  <div class="bg-#eff2f5 px-30px py-30px">
    <div class="bg-white mb-30px px-20px py-30px detail-box">
      <div class="flex items-start justify-start">
        <span>物品信息：</span>
        <div>
          <div v-for="(item, index) of orderInfo.goods.split('-')"
               :key="index">{{ item }}</div>
        </div>
      </div>
      <div>
        <span>国内承运：</span>
        <span>{{ orderInfo.expressName }}</span>
      </div>
      <div class="flex items-center">
        <span>物流单号：</span>
        <span class="mr-10px">{{ orderInfo.expressNumber }}</span>
        <van-button round
                    type="primary"
                    size="mini"
                    plain
                    @click="copy(orderInfo.expressNumber)">复制</van-button>
      </div>

      <p v-if="orderInfo.status === 2"
         class="c-#FC6443! m-0!">物流已发货请注意查收~</p>
    </div>

    <div class="py-30px bg-white">
      <van-steps v-if="detail.length !== 0"
                 direction="vertical"
                 :active="0">
        <van-step v-for="(item, index) in detail"
                  :key="index">
          <h4 class="m-0">{{ item.context }}</h4>
          <h5 class="my-20px">{{ item.time }}</h5>
        </van-step>
      </van-steps>


      <van-empty v-if="detail.length === 0"
                 description="无物流信息" />
    </div>

  </div>
</template>

<script lang='ts' setup>
import { useRoute } from 'vue-router'
import { searchLogistics, Logistics } from '@/api'
import point from '@/script/point'
import { copyUrl } from '@/utils'
import { showToast } from 'vant'
import 'vant/es/toast/style'

const route = useRoute()

const detail = ref<Logistics[]>([])
const orderInfo = computed(() => {
  return JSON.parse(route.query.order as any)
})

onMounted(async () => {
  point.h5Post('getLogisticsDetailsPageH5')
  const res = await searchLogistics(orderInfo.value)
  detail.value = res.list
})

function copy(text) {
  copyUrl(text)
  showToast('复制成功')
}
</script>

<style scoped lang='scss'>
.detail-box {
  color: #646566;
  > div {
    margin-bottom: 20px;
  }
}
</style>
