# 帮助中心

## 项目地址

* [测试环境](https://7to12-test.yangcong345.com/kefu-web-pc/help/)
* [预发环境](https://7to12-stage.yangcong345.com/kefu-web-pc/help/)
* [生产环境](https://7to12.yangcong345.com/kefu-web-pc/help/)

## 迭代

* [立项 2023-11-10](https://project.feishu.cn/wuhan/story/detail/18613664)
* [帮助中心UI调整优化 2023-12-07](https://project.feishu.cn/wuhan/story/detail/19148433)
* [帮助中心UI调整优化 2023-12-15](https://project.feishu.cn/wuhan/story/detail/19148433)
* [帮助中心UI调整优化 2023-12-27](https://project.feishu.cn/wuhan/story/detail/26045377)
* [帮助中心-工单进度用户自查 2024-01-05](https://project.feishu.cn/wuhan/story/detail/3001141467)
* [帮助中心支持千人千面 2024-01-05](https://project.feishu.cn/wuhan/story/detail/3575785032)
* [帮助中心端外环境支持打开 2024-05-15](https://project.feishu.cn/wuhan/story/detail/3827815343)
* [帮助中心改造 2024-05-15](https://project.feishu.cn/wuhan/story/detail/3575728515)

## 注意事项

### browserClose

帮助中心是原生包裹了一个web容器，没法关闭原生

调用 browserClose的时候，data参数需要传一个  removeVC = YCCustomServiceQAVC

<https://yapi.yc345.tv/project/1983/interface/api/42715>

```js
import { callNative } from '@guanghe-pub/onion-utils'

callNative('browserClose', {
  removePage: 'YCCustomServiceQAVC'
})
```
