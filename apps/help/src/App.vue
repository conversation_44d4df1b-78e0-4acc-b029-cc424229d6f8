<!--
 * @Date         : 2021-04-19 17:49:35
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->


<template>
  <router-view v-slot="{ Component }">
    <transition name="slide-left"
                appear
                mode="out-in">
      <keep-alive :include="keepAliveList">
        <component :is="Component"
                   :key="$route.name" />
      </keep-alive>
    </transition>
  </router-view>

  <ImagePreview />
</template>

<script lang="ts" setup>
import { preFun, setSafeCss } from '@/utils/preFun'
import router from '@/router'
import { usePoint } from './store/point'
import ImagePreview from '@/components/ImagePreview/index.vue'
import { useConfig } from './store/config'

onMounted(async () => {
  preFun()
  setSafeCss()

  useConfig().getUserLayered()
})

onBeforeMount(() => {
  usePoint().getPoint()
})

const keepAliveList = ref<string[]>([])
for (const item of router.options.routes) {
  if (item?.meta?.keepAlive && item?.name) {
    keepAliveList.value.push(item?.name as string)
  }
}
</script>

<style lang="scss">
body {
	margin: 0;
}

.van-toast {
  background: var(--van-toast-background) !important;
}

.slide-left-enter-from {
	transform: translateX( -20px);
	opacity: 0;
}

.slide-left-enter-to {
	transform: translateX(0px);
}

.slide-left-leave-from {
	transform: translateX(0);
}

.slide-left-leave-to {
	transform: translateX(20px);
	opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
	transition: all 0.3s;
}

.disable-time-toast {
  width: auto !important;
}
</style>
