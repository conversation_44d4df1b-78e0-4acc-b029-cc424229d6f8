import Axios from '../axios'

export interface HotQuestion {
  docId: string
  baseQuestion: string
}
export interface Qanda {
  solutionSetId: number
  score: number
  qandaUniqueId: string
  baseQuestion: string
  docId: string
}
export interface Solution {
  id: number
  createdAt: string
  updatedAt: string
  deletedAt: string
  solutionId: number
  name: string
  score: number
  qandaSet: Qanda[]
}
/**
 * @description: 获取帮助中心热门问题和常见问题
 */
export function getHotQuestion(): Promise<{
  hotQandas: HotQuestion[]
  solutionSet: Solution[]
  menus: any[]
}> {
  return Axios.get('/web/helpCenterConfig')
}

export interface QuestionDetail {
  baseQuestion: string
  relatedQandas: {
    docId: string
    qandaBaseQuestion: string
    qandaUniqueId: string
  }[]
  answer: string
  docId: string
  qandaQuestions: {
    qandaQuestionId: string
    qandaUniqueId: string
    question: string
    trainId: string
    updatedAt: string
  }[]
  qandaUniqueId: string
  relatedGuideSentence: string
}
/**
 * @description: 获取词条对应的信息
 * @param {string} docId
 */
export function getQuestionDetail(docId: string): Promise<QuestionDetail> {
  return Axios.get(`/web/qanda/search/${docId}`)
}
export interface Question {
  matchMode: string
  baseQuestion: string
  otherQuestion: string[]
  relatedQandas: {
    qandaBaseQuestion: string
    qandaUniqueId: number
  }[]
  qandaCategoryId: number
  answer: string
  status: boolean
  effective: number
  expire: number
  docId: string
  createdAt: string
  updatedAt: string
  deletedAt: string
  qandaQuestions: {
    qandaUniqueId: string
    question: string
    qandaQuestionId: string
    updatedAt: string
    trainId: string
  }[] // 关联的相似问题数据，列表接口主要用来计数
  qandaUniqueId: string // 唯一id
}
/**
 * @description: 获取问答列表
 */
export function getQuestionList(par: {
  pages: number
  pageSize: number
  qandaCategoryId?: number // 问答目录id
  searchMode?: 'question' | 'answer' // 查询方式
  content: string // 查询内容
  iShield?: boolean // 是否屏蔽字符串，查询全部不传该参数，查询屏蔽的传true字符串，查询未屏蔽的词条传false字符串
}): Promise<Question> {
  return Axios.get('/admin/qanda', par)
}


export interface SearchGuideItem {
  docId: string
  question: string
}
/**
 * @description: 检索知识库引导
 * @url /web/qanda/search
 */
export function searchGuide(keyword: string): Promise<{
  list: SearchGuideItem[]
}> {
  return Axios.post('/web/qanda/helpCenterSearch', { keyword })
}


/**
 * @description: 用户分层展示
 */
export function userLayered(par: {
  onionUserId: string
}): Promise<{
  show: boolean
}> {
  return Axios.post('/web/userShow', par)
}

/**
 * @description: 获取公告内容
 */
export function getNotice(): Promise<{
  title: string
  content: string
}> {
  return Axios.get('/web/notice/read')

}
