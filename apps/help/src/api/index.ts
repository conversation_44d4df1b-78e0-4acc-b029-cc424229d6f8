import Axios from './axios'
import { getQueryObject } from '@guanghe-pub/onion-utils'

import * as help from './modules/help'
import * as portal from './modules/portal'

export interface ServiceBoard {
  title: string
  content: string
  disableBeginAt: string
  disableEndAt: string
  disableText: string
}

export default {
  getServiceBoard(): Promise<ServiceBoard> {
    return Axios.get('/web/serviceBoard')
  },

  getABGroup() {
    const { userId, token, omoum, clientType, clientVersion } = getQueryObject()
    return Axios.get(`${import.meta.env.VITE_BASEHOST}/backend/xlab/abgroup/c3170386a7b4d99de012ea5b75da47b2`, {}, {
      headers: {
        uid: userId,
        Authorization: token,
        omoum,
        'content-type': 'multipart/form-data',
        referer: 12,
        'client-version': clientVersion,
        'client-type': clientType
      }
    })
  },

  ...help,
  ...portal
}
