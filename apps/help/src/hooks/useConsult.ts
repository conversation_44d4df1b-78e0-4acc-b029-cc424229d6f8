/*
 * @Date         : 2023-11-21 17:08:43
 * @Description  : 在线咨询、电话咨询按钮
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { browserTelephone, browserJump, browserTitle } from '@guanghe-pub/onion-utils'
import helpPoint from '@/script/helpPoint'
import { useServiceBoard } from '@/store/serviceBoard'
import { showFailToast } from 'vant'
import 'vant/es/toast/style'
import api from '@/api'
import openUrl from '#/script/openUrl'
import { usePoint } from '@/store/point'

export default function() {
  const serviceBoardStore = useServiceBoard()

  function callTel() {
    helpPoint.post('clickHelpCenterCustomerServicePopup', {
      button: '电话咨询'
    })

    if (serviceBoardStore.checkDisableTime()) {
      showFailToast({
        message: serviceBoardStore.disableText,
        className: 'disable-time-toast'

      })
      throw Error('拨打客服电话禁用时间')
    }

    try {
      browserTelephone('4000108088')
    } catch (error) {
      showFailToast('不能直接跳转，号码为4000108088，请手动跳转')
    }
  }

  async function goConsult() {
    helpPoint.post('clickHelpCenterCustomerServicePopup', {
      button: '在线咨询'
    })

    let url = ''

    const { isCustomPad, fromPageName } = usePoint()
    if (isCustomPad === 'true') {  // 区分第三方管控平板 跳转平板渠道
      if (import.meta.env.VITE_ENV === 'test' || import.meta.env.VITE_ENV === 'development') {  // 平板
        url = 'https://7to12-test.yangcong345.com/kefu-web-pc/mobile?channelId=dcac7bf0-0078-4f70-a867-091f84599cb7'
      } else if (import.meta.env.VITE_ENV === 'stage') {
        url = 'https://7to12-stage.yangcong345.com/kefu-web-pc/mobile?channelId=ca10a17c-1120-40b9-b1e0-1c836f9f2873'
      } else if (import.meta.env.VITE_ENV === 'master') {
        url = 'https://7to12.yangcong345.com/kefu-web-pc/mobile?channelId=3f189b6a-43db-48ec-b6f4-f0c02cf6c566'
      }
    } else {  // 手机
      if (import.meta.env.VITE_ENV === 'test' || import.meta.env.VITE_ENV === 'development') {
        url = 'https://7to12-test.yangcong345.com/kefu-web-pc/mobile?channelId=be93287a-aa27-4d04-8e07-2ca8672e6045'
      } else if (import.meta.env.VITE_ENV === 'stage') {
        url = 'https://7to12-stage.yangcong345.com/kefu-web-pc/mobile?channelId=982151ce-919e-4915-a065-9e1f6567f0d6'
      } else if (import.meta.env.VITE_ENV === 'master') {
        url = 'https://7to12.yangcong345.com/kefu-web-pc/mobile?channelId=e30b5dcb-ff61-4f2f-8f70-b881891a1134'
      }
    }

    // browserTitle('洋葱客服')
    // openUrl(url, '_self')
    browserJump({
      title: '洋葱客服',
      url: `${url}&fromPageName=${fromPageName}`,
    })
  }

  function goConsultTeacher() {
    helpPoint.post('clickHelpCenterCustomerServicePopup', {
      button: '在线咨询'
    })

    let url = ''

    if (import.meta.env.VITE_ENV === 'test' || import.meta.env.VITE_ENV === 'development') {
      url = 'https://7to12-test.yangcong345.com/kefu-web-pc/mobile?channelId=ac171322-68f0-493a-85ec-2c1b55330aff'
    } else if (import.meta.env.VITE_ENV === 'stage') {
      url = 'https://7to12-stage.yangcong345.com/kefu-web-pc/mobile?channelId=e4346224-dea5-4a5b-9764-f7aa8b2caf44'
    } else if (import.meta.env.VITE_ENV === 'master') {
      url = 'https://7to12.yangcong345.com/kefu-web-pc/mobile?channelId=f428c215-d13e-493e-abb5-530d8770f72f'
    }

    openUrl(url, '_self', true)
  }

  return { callTel, goConsult, goConsultTeacher }
}
