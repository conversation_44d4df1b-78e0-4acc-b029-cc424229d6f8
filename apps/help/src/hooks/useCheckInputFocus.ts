/*
 * @Date         : 2023-12-26 12:30:12
 * @Description  : 判断当前页面有没有安卓 input焦点事件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

export default function() {
  const originH = ref(document.documentElement.clientHeight || document.body.clientHeight)
  const resizeH = ref(document.documentElement.clientHeight || document.body.clientHeight)
  const isFocus = ref(false)

  watch(resizeH, newV => {
    if (newV !== originH.value) {
      isFocus.value = true
    } else {
      isFocus.value = false
    }
  })

  function setResizeH() {
    resizeH.value = document.documentElement.clientHeight || document.body.clientHeight
  }

  onMounted(() => {
    window.addEventListener('resize', setResizeH)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', setResizeH)
  })

  return { isFocus }
}
