<?xml version="1.0" encoding="UTF-8"?>
<svg fill="currentColor" width="25px" height="22px" viewBox="0 0 25 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>shuaxin备份</title>
    <g id="页面-1" stroke="none" stroke-width="1">
        <g id="咨询首页" transform="translate(-573.000000, -307.000000)" fill-rule="nonzero">
            <g id="shuaxin备份" transform="translate(573.000000, 307.000000)">
                <path d="M12.5,19.9968396 C9.60238744,19.9899659 6.90286994,18.4971204 5.31818182,16.0252611 L3.04545453,16.0252611 C4.82577373,19.6865143 8.48995999,22.0020721 12.5,21.9999986 C18.0254292,21.9515018 22.5983717,17.6085857 23.0340909,11.9957879 L25,11.9957879 L22.1136364,8.00105161 L19.2272727,11.9957879 L21.1022727,11.9957879 C20.6612585,16.510078 16.9520964,19.9600032 12.5,19.9968396 Z M12.5,0 C7.35462625,0.0414590195 2.97619305,3.8299857 2.10227272,8.99684097 L0,8.99684097 L2.88636363,13.0031562 L5.77272726,8.99684097 L4.05681818,8.99684097 C4.93662323,4.958017 8.43988859,2.0753985 12.5,2.04947343 C15.0119014,2.05368872 17.3958148,3.17936085 19.0227273,5.12947303 L19.3977273,5.03684147 L21.3522727,5.03684147 C19.4467845,1.91967822 16.1040654,0.0177094735 12.5,0 Z" id="形状"></path>
            </g>
        </g>
    </g>
</svg>
