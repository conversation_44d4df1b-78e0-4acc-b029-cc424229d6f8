import { BuryPoint, isInNative } from '@guanghe-pub/onion-utils'
import axios from 'axios'
import { usePoint } from '@/store/point'
import { useUser } from '@/store/user'

const points = {
  getHelpCenterHome: {
    category: 'activity',
    data: ['fromPageName'],
    desc: '帮助中心落地页曝光 来源[我的页面、三方管控平板、其他]',
  },
  clickHelpCenterHomeButton: {
    category: 'activity',
    data: ['fromPageName', 'button'],
    desc: '帮助中心落地页按钮点击 点击的按钮[意见反馈、咨询客服]',
  },

  getHelpCenterPopup: {
    category: 'activity',
    data: ['fromPageName'],
    desc: '帮助中心咨询客服弹窗曝光',
  },
  clickHelpCenterCustomerServicePopup: {
    category: 'activity',
    data: ['fromPageName', 'button'],
    desc: '咨询客服弹窗按钮点击 点击的按钮[在线咨询、电话咨询、关闭弹窗]',
  },

  clickSCPFSubmit: {
    category: 'setting',
    data: ['text'],
    decs: '意见反馈页面，点击提交反馈'
  },
  enterSCPFeedback: {
    category: 'setting',
    decs: '进入意见反馈页面'
  },
  getHelpCenterAnswerPage: {
    category: 'activity',
    data: ['actId'],
    desc: '帮助中心答案页曝光'
  },

  clickHelpCenterHomeMenu: {
    category: 'activity',
    data: ['actId', 'button', 'fromPageName'],
    desc: '帮助中心首页菜单点击'
  },

  getScenarioProblemList: {
    category: 'activity',
    data: ['actId'],
    desc: '帮助中心常见问题分类页曝光'
  },

  getHelpCenterBulletinBoard: {
    category: 'activity',
    data: ['fromPageName'],
    desc: '曝光帮助中心公告明细页',
  },
}

const helpPoint = new BuryPoint(points, {
  contextData: {
    fromPageName: getFromPageName()
  },
  env: import.meta.env.VITE_GETHOST,
  axios
})

export default helpPoint

function getFromPageName() {
  const pointStore = usePoint()

  if (pointStore.fromPageName === '我的页面' || pointStore.fromPageName === '帮助中心') return '我的页面'

  if (pointStore.isCustomPad === 'true') return '三方管控平板'

  return '其他'
}

const h5Points = {
  getHelpCenterHome: {
    category: 'activity',
    data: ['fromChannel'],
    desc: '帮助中心落地页曝光 来源[我的页面、三方管控平板、其他]',
  },
  clickHelpCenterHomeButton: {
    category: 'activity',
    data: ['fromChannel', 'button'],
    desc: '帮助中心落地页按钮点击 点击的按钮[意见反馈、咨询客服]',
  },

  getHelpCenterAnswerPage: {
    category: 'activity',
    data: ['fromChannel', 'actId'],
    desc: '帮助中心答案页曝光'
  },

  clickHelpCenterHomeMenu: {
    category: 'activity',
    data: ['fromChannel', 'actId', 'button'],
    desc: '帮助中心首页菜单点击'
  },

  getScenarioProblemList: {
    category: 'activity',
    data: ['fromChannel', 'actId'],
    desc: '帮助中心常见问题分类页曝光'
  },

  getHelpCenterBulletinBoard: {
    category: 'activity',
    data: ['fromChannel'],
    desc: '曝光帮助中心公告明细页',
  },
}

const helpH5Point = new BuryPoint(h5Points, {
  contextData: {
    productId: '427',
    fromChannel: usePoint().channelId
  },
  env: import.meta.env.VITE_GETHOST,
  axios
})

export function checkPointEnv(key, par = {}) {
  const isInNativeFlag = isInNative()
  console.log('isInNativeFlag', isInNativeFlag)
  if (isInNativeFlag) {
    helpPoint.post(key, par)
  } else {
    helpH5Point.h5Post(key, par)
  }
}
