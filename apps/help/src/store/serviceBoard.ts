import { defineStore } from 'pinia'
import api from '@/api'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

interface ServiceBoardState {
  title: string
  content: string
  disableBeginAt: string
  disableEndAt: string
  disableText: string
}

export const useServiceBoard = defineStore({
  id: 'ServiceBoard',
  state(): ServiceBoardState {
    return {
      title: '',
      content: '',
      disableBeginAt: '',
      disableEndAt: '',
      disableText: ''
    }
  },
  getters: {
  },
  actions: {
    async getServiceBoard() {
      const info = await api.getServiceBoard()

      this.title = info.title
      this.content = info.content
      this.disableBeginAt = info.disableBeginAt
      this.disableEndAt = info.disableEndAt
      this.disableText = info.disableText
    },
    checkDisableTime() {
      const disableBeginAtArr = this.disableBeginAt.split(':').map(item => Number(item))
      const disableEndAtArr = this.disableEndAt.split(':').map(item => Number(item))

      let beginTime = dayjs().hour(disableBeginAtArr[0]).minute(disableBeginAtArr[1]).second(disableBeginAtArr[2])
      let endTime = dayjs().hour(disableEndAtArr[0]).minute(disableEndAtArr[1]).second(disableEndAtArr[2])

      if (beginTime.isAfter(endTime)) {  // 跨天
        endTime = endTime.add(1, 'day')
      }

      let afterBeginTime = beginTime.subtract(1, 'day')
      let afterEndTime = endTime.subtract(1, 'day')

      dayjs.extend(isBetween)
      return dayjs().isBetween(beginTime, endTime) || dayjs().isBetween(afterBeginTime, afterEndTime)
    }
  },
})
