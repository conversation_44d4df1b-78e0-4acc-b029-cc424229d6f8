import { browserGETMe, getQueryObject } from '@guanghe-pub/onion-utils'
import { defineStore } from 'pinia'

interface UserState {
  userId: string
  onionId: string
  gender: string  // 性别
  phone: string // 手机号
  uname: string // 昵称
  realIdentity: string  // 身份
  attribution: string // 用户归属

  fromPageName: string
  appVersion: string
  platform: string  // os
  phoneModel: string  // 手机型号
}

export const useUser = defineStore({
  id: 'UserInfo',
  state(): UserState {
    return {
      userId: '',
      onionId: '',
      gender: '',
      phone: '',
      uname: '',
      realIdentity: '',
      attribution: '',

      fromPageName: '',
      appVersion: '',
      platform: '',
      phoneModel: ''
    }
  },
  getters: {
    userParam(state: UserState) {
      const paramKey = [
        'userId', 'onionId', 'gender', 'phone', 'uname', 'realIdentity', 'attribution',
        'fromPageName', 'appVersion', 'platform', 'phoneModel'
      ]
      let target = ''
      for (const item of paramKey) {
        target += `${item}=${state[item]}&`
      }

      target += `from=${this.fromPageName}`
      return target
    },
    isInApp(state: UserState): Boolean {
      return Boolean(this.userId)
    }
  },
  actions: {
    async setInfo() {
      const { appVersion, platform, phoneModel, fromPageName, userId } = getQueryObject()
      if (fromPageName) this.fromPageName = fromPageName
      if (appVersion) this.appVersion = appVersion
      if (platform) this.platform = platform
      if (phoneModel) this.phoneModel = phoneModel
      if (userId) this.userId = userId

      const userInfo:any = await browserGETMe()
      if (userInfo) {
        this.gender = userInfo.gender
        this.phone = userInfo.phone
        this.uname = userInfo.uname
        this.realIdentity = userInfo.realIdentity
        this.onionId = userInfo.onionId
        this.attribution = userInfo.attribution
      }
    },
    splitPortal(url) {
      const target = url + `${url.includes('?') ? '&' : '?'}${this.userParam}`
      return target
    }
  },
  persist: true
})
