import { defineStore } from 'pinia'
import api from '@/api'
import { HotQuestion, Solution } from '@/api/modules/help'
import { Portal } from '@/api/modules/portal'
import { usePoint } from '@/store/point'
import { useUser } from './user'

interface ConfigState {
  helpCenterConfig: {
    hotQandas: HotQuestion[]
    solutionSet: Solution[]
    menus: any[]
  }

  detailImgList: string[]
  chooseImg: string

  portalList: Portal[]

  showMiddleJumptelsale: boolean // 是否展示学习规划师（middle/jumptelsale）
  showPortal: boolean // 是否展示传送门

  currentClassifyId: number // 选择的分类id
}

export const useConfig = defineStore({
  id: 'Config',
  state(): ConfigState {
    return {
      helpCenterConfig: {
        hotQandas: [],
        solutionSet: [],
        menus: []
      },
      detailImgList: [],
      chooseImg: '',

      portalList: [],

      showMiddleJumptelsale: false,
      showPortal: true,

      currentClassifyId: 0,
    }
  },
  getters: {
    menus(state): any[] {
      if (!this.showMiddleJumptelsale) {  // 判断隐藏中间页常用工具
        return this.helpCenterConfig.menus.filter(item => {
          return !item.menuDescription?.content.includes('middle/jumptelsale')
        })
      }

      return this.helpCenterConfig.menus
    },
    currentClassify(state): any {
      return this.helpCenterConfig.solutionSet.find(item => item.id === state.currentClassifyId)
    },
  },
  actions: {
    async getHelpCenterConfig() {
      const res:any = await api.getHotQuestion()
      this.helpCenterConfig = res
    },
    async getPortal() {
      const res = await api.getPortal()
      this.portalList = res.list

      if (!useUser().isInApp) {
        this.showPortal = false
      }
    },
    checkPortalList(url) {
      return this.portalList.find(item => item.link === url)
    },
    async getUserLayered() {  // 判断用户分层
      const helpChannel = ['be93287a-aa27-4d04-8e07-2ca8672e6045', '982151ce-919e-4915-a065-9e1f6567f0d6', 'e30b5dcb-ff61-4f2f-8f70-b881891a1134']
      if (!helpChannel.includes(usePoint().channelId) && usePoint().channelId) {
        this.showMiddleJumptelsale = false
        return
      }

      const userId = usePoint().userId
      if (userId) {
        const res = await api.userLayered({
          onionUserId: userId,
        })
        this.showMiddleJumptelsale = res.show
      } else {
        this.showMiddleJumptelsale = false
      }
    }
  },
  persist: {
    paths: ['currentClassifyId', 'showMiddleJumptelsale']
  }
})
