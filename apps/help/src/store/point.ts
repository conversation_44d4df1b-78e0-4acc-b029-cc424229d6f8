import { defineStore } from 'pinia'
import { getQueryObject } from '@guanghe-pub/onion-utils'

interface PointState {
  fromPageName: string
  isCustomPad: string
  channelId: string
  userId: string
  appVersion: string
}

export const usePoint = defineStore({
  id: 'Point',
  state(): PointState {
    return {
      fromPageName: '',
      isCustomPad: '',
      channelId: '',
      userId: '',
      appVersion: ''
    }
  },
  getters: {
  },
  actions: {
    getPoint() {
      const { fromPageName, isCustomPad, channelId, userId, appVersion } = getQueryObject()
      if (fromPageName) this.fromPageName = fromPageName
      if (isCustomPad) this.isCustomPad = isCustomPad
      if (channelId) this.channelId = channelId
      if (userId) this.userId = userId
      if (appVersion) this.appVersion = appVersion
    }
  },
  persist: true
})
