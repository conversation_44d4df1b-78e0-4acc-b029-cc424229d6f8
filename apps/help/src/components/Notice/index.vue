<!--
 * @Date         : 2024-04-07 10:33:10
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <Mobile v-if="!isPad" />
    <Pad v-if="isPad" />
  </div>
</template>

<script lang='ts' setup>
import Mobile from './mobile.vue'
import Pad from './pad.vue'

const mediaQueryList = window.matchMedia('(orientation: landscape)')
const isPad = ref(false)

if (mediaQueryList.matches) {
  console.log('横屏')
  isPad.value = true
} else {
  console.log('竖屏')
  isPad.value = false
}
</script>

<style scoped lang='scss'>
.notice-box {
  background: url('https://fp.yangcong345.com/middle/1.0.0/help/notice/background-3cc9ee2960f0c8e44a93d8aa5d53933d__w.png') no-repeat;
  background-size: 100% 100%;
  width: 710PX;
  height: 300PX;
}
</style>
