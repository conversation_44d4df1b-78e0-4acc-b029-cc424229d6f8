<!--
 * @Date         : 2024-11-27 10:48:39
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-100% flexD pad-notice-container box-border">
    <div class="notice-box box-border">
      <div class="flex items-center">
        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/notice/icon-416ef066b319749ef3cde00d2fdf60b0__w.png"
                        class="icon"
                        alt="公告" />

        <div class="c-#129CFF font-bold title">文明公告</div>
      </div>

      <div class="c-#333 content">
        您的建议和问题反馈，我们都会一一查阅并尽可能优化改进，可能暂时无法一一回复您，但不会停止处理问题的脚步哦。希望您可以文明描述您遇到的情况，以及想要我们做的功能优化！感谢您的包容和理解~
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>

</script>

<style scoped lang='scss'>
@function calcPx($px) {
  @return calc(100vw / 1180 * $px)
}

.pad-notice-container {
  margin-top: calcPx(10);
  padding: 0 calcPx(37);
}

.notice-box {
  background: url('@/assets/notice/feedback_pad_bg.png') no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: calcPx(178);
  padding: calcPx(36) calcPx(63) 0 calcPx(63);
}

.icon {
  width: calcPx(31);
  height: calcPx(31);
}

.title {
  font-size: calcPx(23);
  margin-left: calcPx(13);
}

.content {
  font-size: calcPx(17);
  line-height: calcPx(27);
  width: calcPx(980);
  margin-top: calcPx(10);
}
</style>
