<!--
 * @Date         : 2024-11-27 11:08:39
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-100% flexD mt-13px">
    <div class="notice-box box-border pt-50px pl-55px">
      <div class="flex items-center">
        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/notice/icon-416ef066b319749ef3cde00d2fdf60b0__w.png"
                        class="w-44px h-44px"
                        alt="公告" />

        <div class="text-30px c-#129CFF font-bold ml-13px">文明公告</div>
      </div>

      <div class="c-#333 text-23px line-height-33px w-600px mt-26px">
        您的建议和问题反馈，我们都会一一查阅并尽可能优化改进，可能暂时无法一一回复您，但不会停止处理问题的脚步哦。希望您可以文明描述您遇到的情况，以及想要我们做的功能优化！感谢您的包容和理解~
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>

</script>

<style scoped lang='scss'>
.notice-box {
  background: url('https://fp.yangcong345.com/middle/1.0.0/help/notice/background-3cc9ee2960f0c8e44a93d8aa5d53933d__w.png') no-repeat;
  background-size: 100% 100%;
  width: 710px;
  height: 300px;
}
</style>
