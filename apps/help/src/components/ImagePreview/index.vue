<!--
 * @Date         : 2023-11-22 12:27:14
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-12-25 18:58:02
-->

<template>
  <div class="preview-box">
    <van-image-preview v-model:show="show"
                       :before-close="beforeClose"
                       :start-position="startPosition"
                       :images="configStore.detailImgList"
                       closeable
                       @click="show = false" />
  </div>
</template>

<script lang='ts' setup>
import { useConfig } from '@/store/config'

const show = ref(false)
const configStore = useConfig()
const startPosition = ref(0)


watch(show, newV => {
  if (newV) {
    startPosition.value = configStore.detailImgList.findIndex(item => item === configStore.chooseImg)
  }
})

function beforeClose(e) {
  show.value = false
}

onMounted(() => {
  window.showImagePreview = function(img:string) {
    configStore.chooseImg = img
    show.value = true
  }
})
</script>

<style scoped lang='scss'>
.preview-box {
  :deep(.van-image-preview__swipe-item) {
    overflow: auto !important;
  }
}
</style>
