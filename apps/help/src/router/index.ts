import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Help',
    component: () => import('@/views/Help/index.vue'),
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('@/views/Help/index.vue'),
  },
  {
    path: '/feedback',
    name: 'Feedback',
    component: () => import('@/views/Feedback/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/Help/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/teacher',
    name: 'Teacher',
    component: () => import('@/views/Teacher/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/QuestionDetail',
    name: 'QuestionDetail',
    component: () => import('@/views/QuestionDetail/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/QuestionClassify',
    name: 'QuestionClassify',
    component: () => import('@/views/QuestionClassify/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/Notice',
    name: 'Notice',
    component: () => import('@/views/Notice/index.vue'),
    meta: {
      keepAlive: false
    }
  },
]

const router = createRouter({
  history: createWebHistory('kefu-web-pc/help'),
  routes,
})

router.beforeEach(beforeEach)

export default router
