<!--
 * @Date         : 2024-05-14 16:15:34
 * @Description  : 常见问题分类
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flex h-100vh">
    <div class="h-100% flex flex-col w-224px bg-#F1FBFF">
      <div v-for="(item, index) in configStore.helpCenterConfig.solutionSet"
           :key="index"
           :class="{sideActive: configStore.currentClassifyId === item.id}"
           class="sidebar flexD w-100% h-110px"
           @click="configStore.currentClassifyId = item.id">
        <span class="c-#3B3A4A text-30px font-600">{{ item.name }}</span>
      </div>
    </div>


    <div class="flex-1 h-100% overflow-auto">
      <div v-for="(item, index) in configStore.currentClassify.qandaSet"
           :key="index"
           class="flex items-center justify-start px-32px py-39px"
           @click="chooseQuestion(item)">
        <span class="c-#3B3A4A text-28px font-400 textHideLine1">{{ item.baseQuestion }}</span>
      </div>

      <div class="flexD py-39px">
        <span class="c-#CBCBCB text-28px font-400">没有更多了</span>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useConfig } from '@/store/config'
import useGoDetail from '../Help/hooks/useGoDetail'
import { checkPointEnv } from '@/script/helpPoint'

const { goDetail } = useGoDetail()
const configStore = useConfig()

function chooseQuestion(item) {
  goDetail(item.docId, '常见问题分类')
}

onMounted(() => {
  checkPointEnv('getScenarioProblemList', {
    actId: configStore.currentClassifyId
  })
})
</script>

<style scoped lang='scss'>
.sideActive {
  background: white;
  position: relative;
  span {
    color: #2CABFB;
  }
  ::before {
    content: '';
    width: 7px;
    height: 42px;
    background: #2CABFB;
    position: absolute;
    left: 0;
    top: calc(50% - 21px);
  }
}

</style>
