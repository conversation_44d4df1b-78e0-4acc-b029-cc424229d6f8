<!--
 * @Date         : 2024-10-30 16:38:04
 * @Description  : 公告页
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="pt-40px px-54px">
    <div class="flexD w-100% text-#019ef8 mb-30px">
      <van-icon name="volume"
                class="text-32px mr-10px" />
      <span class="text-30px font-600">{{ notice.title }}</span>
    </div>

    <div class="break-words reply-text overflow-auto text-28px c-#3B3A4A"
         v-html="notice.content" />
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'
import { checkPointEnv } from '@/script/helpPoint'

const notice = ref<{
  content: string
  title: string
}>({
  content: '',
  title: '',
})

onMounted(async () => {
  checkPointEnv('getHelpCenterBulletinBoard')
  notice.value = await api.getNotice()
})
</script>

<style scoped lang='scss'>
.reply-text {
  white-space: pre-wrap;
  :deep(img) {
    max-width: 100%;
    max-height: fit-content;
  }
  :deep(video) {
    max-width: 100%;
  }
}
</style>
