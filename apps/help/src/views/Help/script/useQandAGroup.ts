/*
 * @Date         : 2023-07-21 14:34:57
 * @Description  : 常见问题换一批功能
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-12-01 16:38:54
 */
export default function(nowGroup) {
  const checkQandaIndex = ref({})

  // 点击换一批切换后五条
  function calcQandaSet() {
    const check = checkQandaIndex.value[nowGroup.value.id]

    if (!check) {
      checkQandaIndex.value[nowGroup.value.id] = 1
    } else {
      checkQandaIndex.value[nowGroup.value.id] += 1
    }

    if (checkQandaIndex.value[nowGroup.value.id] * 5 >= nowGroup.value.qandaSet.length) {
      checkQandaIndex.value[nowGroup.value.id] = 0
    }
  }

  // 计算显示五条
  function computedGroup() {
    if (!checkQandaIndex.value[nowGroup.value.id] && checkQandaIndex.value[nowGroup.value.id] !== 0) {
      return nowGroup.value.qandaSet.slice(0, 5)
    } else {
      return nowGroup.value.qandaSet.slice(checkQandaIndex.value[nowGroup.value.id] * 5, (checkQandaIndex.value[nowGroup.value.id] + 1) * 5)
    }
  }

  return { computedGroup, calcQandaSet }
}
