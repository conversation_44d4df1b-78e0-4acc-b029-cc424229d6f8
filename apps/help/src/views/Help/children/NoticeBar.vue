<!--
 * @Date         : 2024-10-30 15:51:59
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="notice?.title"
       class="bg-#FFF071 h-60px w-100% flex items-center"
       @click="router.push('Notice')">
    <div class="px-50px textHideLine1 c-#FE5969!">
      <van-icon name="volume"
                class="text-32px mr-10px" />

      <span class="text-28px ">
        <span class="font-600">公告：</span>
        <span>{{ notice?.title }}</span>
      </span>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useRouter } from 'vue-router'
import api from '@/api'

const router = useRouter()

const notice = ref()
onMounted(async () => {
  notice.value = await api.getNotice()
})
</script>

<style scoped lang='scss'>

</style>
