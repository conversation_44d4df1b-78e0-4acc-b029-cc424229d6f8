<!--
 * @Date         : 2023-12-22 15:59:10
 * @Description  : 常用工具
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="configStore.helpCenterConfig.menus.length !== 0"
       class="mt-23px bg-white px-42px commonTool-box">
    <Title title="常用工具" />

    <van-swipe>
      <van-swipe-item v-for="(it, idx) in splitMenus"
                      :key="idx"
                      :class="{'pb-50px':splitMenus.length > 1, 'grid-rows-2': it.length > 4 }"
                      class="grid grid-cols-4 gap-y-32px">
        <div v-for="(item, index) in it"
             :key="index"
             class="flexD flex-col"
             @click="clickMenu(item)">
          <img :src="item.menuDescription.icon"
               class="w-46px h-46px mb-11px">
          <div class="text-24px">{{ item.name }}</div>
        </div>
      </van-swipe-item>
    </van-swipe>

    <div v-if="splitMenus.length < 2"
         class="h-20PX" />
  </div>
</template>

<script lang='ts' setup>
import { useConfig } from '@/store/config'
import { checkPointEnv } from '@/script/helpPoint'
import { openH5WithNewWebview } from '@guanghe-pub/onion-utils'
import { useUser } from '@/store/user'
import { openUrl, replaceUrlHost } from '@kefuweb/utils'
import Title from '@/views/Help/components/Title.vue'

const configStore = useConfig()

const splitMenus = computed(() => {
  let result:any[] = []
  const arr = configStore.menus

  for (let i = 0; i < arr.length; i += 8) {
    let chunk = arr.slice(i, i + 8)
    result.push(chunk)
  }

  return result
})

/**
 * 点击菜单项处理函数
 * @param {any} info - 菜单项信息
 */
function clickMenu(info) {
  checkPointEnv('clickHelpCenterHomeMenu', {
    actId: info.menuId,
    button: info.name
  })

  // 使用hostReplace函数替换URL中的host
  const replacedUrl = replaceUrlHost(info.menuDescription.content)

  if (useUser().userId) {
    // openH5WithNewWebview(replacedUrl)
    openUrl(replacedUrl, '_self')
  } else {
    openUrl(replacedUrl, '_self')
  }
}
</script>

<style scoped lang='scss'>
.commonTool-box {
  :deep(.van-swipe__indicator) {
    background-color: #31302c;
  }
  :deep(.van-swipe__indicator--active) {
    background-color: #1989fa;
  }
}
</style>
