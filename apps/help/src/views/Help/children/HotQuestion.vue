<!--
 * @Date         : 2023-12-01 15:39:19
 * @Description  : 热门问题
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <div v-if="hotQuestionSlice.length !== 0"
         class="my-23px bg-white px-42px py-18px">
      <div class="relative">
        <Title title="热门问题" />

        <div v-if="configStore.helpCenterConfig.hotQandas.length > 5"
             class="text-24px flexD mt-20px mb-5px absolute right-0 top--5px">
          <common-svg name="replay"
                      class="mr-6px"
                      color="#009EF8"
                      size="14px" />
          <span class="line-height-30px c-#009EF8"
                @click="changeHot">换一批</span>
        </div>
      </div>

      <div v-for="(hotQuestion, index) in hotQuestionSlice"
           :key="index"
           class="py-10px flex items-center justify-between b-b b-b-solid b-b-#E9E9E9"
           @click="goDetail(hotQuestion.docId, '热门问题')">
        <span class="flex items-center">
          <span class="mr-20px text-35px font-600 italic"
                :class="`number-${index+1}`">{{ index + 1 }}</span>
          <span class="text-28px c-#3B3A4A font-400 textHideLine1">{{ hotQuestion.baseQuestion }}</span>
        </span>
        <van-icon name="arrow"
                  size="14"
                  color="#3B3A4A" />
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import useQandAGroup from '../script/useQandAGroup'
import useGoDetail from '../hooks/useGoDetail'
import { useConfig } from '@/store/config'
import Title from '@/views/Help/components/Title.vue'

const configStore = useConfig()
const { goDetail } = useGoDetail()

const hotIndex = ref(0)
const hotQuestionSlice = computed(() => {
  return configStore.helpCenterConfig.hotQandas?.slice(hotIndex.value * 5, (hotIndex.value + 1) * 5) ?? []
})
function changeHot() {
  if ((hotIndex.value + 1) * 5 >= configStore.helpCenterConfig.hotQandas.length) {
    hotIndex.value = 0
  } else {
    hotIndex.value++
  }
}
</script>

<style scoped lang='scss'>
.tab-box {
  :deep(.van-tabs__wrap) {
    position: relative;
    overflow: initial;
    &::after {
      content: '';
      height: 1px;
      background-color: #D7EAFF;
      width: calc(100% + 20px);
      display: inline-block;
      position: absolute;
      bottom: 0;
      left: -10px;
    }
  }
  :deep(.van-tabs__content) {
    margin-top: 10px;
  }
  :deep(.van-tab--shrink ) {
    font-size: 30px;
  }
}

.number-1 {
  color: #F94D46
}
.number-2 {
  color: #F96F47
}
.number-3 {
  color: #FB9D43
}
.number-4, .number-5 {
  color: #BBBBBB
}

</style>
