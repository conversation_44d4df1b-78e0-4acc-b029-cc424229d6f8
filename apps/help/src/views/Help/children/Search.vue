<!--
 * @Date         : 2023-12-01 14:55:33
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="search-container">
    <van-search v-model="searchVal"
                background="unset"
                clearable
                placeholder="请输入您问题的关键词～" />

    <div class="relative">
      <TalkSearchGuide :text="searchVal" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import TalkSearchGuide from '../components/TalkSearchGuide.vue'

const searchVal = ref('')

</script>

<style scoped lang='scss'>
.search-container {
  :deep(.van-search__content) {
    border-radius: 8px;
    height: 69px;
    background: #F2F2F2;
    display: flex;
    align-items: center;
  }
  :deep(.van-search__action) {
    color: #4F9FF9;
  }
  :deep(.van-field__control) {
    color: #999 !important;
    font-size: 28px !important;

    ::placeholder {
      color: #999999 !important;
      font-size: 28px !important;
    }
  }
}
</style>
