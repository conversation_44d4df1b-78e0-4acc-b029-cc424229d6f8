<!--
 * @Date         : 2023-12-01 14:41:04
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="(showBtn || showBtnOutside) && !isFocus">
    <div class="h-100px" />

    <div class="bottom-container fixed bottom-0px w-100% py-24px bg-white">
      <div v-if="showBtn && !isFocus"
           class="flex items-center justify-evenly">
        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/opinions_btn-fcb6299e528169194b56ed95bcea93fe__w.png"
                        class="w-194px"
                        alt="咨询客服"
                        @click="goFeedback" />

        <van-divider vertical
                     class="b-#E5E5E5! w-2px! h-44px!" />

        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/consultation_btn-932e2302bca85f08569f246af6aa2700__w.png"
                        class="w-194px"
                        alt="咨询客服"
                        @click="goConsult" />
      </div>

      <div v-if="showBtnOutside && !isFocus"
           class="flex items-center justify-evenly ">
        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/consultation_btn-932e2302bca85f08569f246af6aa2700__w.png"
                        class="w-194px"
                        alt="咨询客服"
                        @click="goConsultOutside" />
      </div>

      <ConsultDialog v-model:visible="consultVisible" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useRouter } from 'vue-router'
import ConsultDialog from '../components/ConsultDialog.vue'
import helpPoint, { checkPointEnv } from '@/script/helpPoint'
import useCheckInputFocus from '@/hooks/useCheckInputFocus'
import { openUrl } from '@kefuweb/utils'
import { usePoint } from '@/store/point'

const router = useRouter()

const consultVisible = ref(false)

// 帮助中心安卓端咨询客服按钮不跟随输入框顶起
const { isFocus } = useCheckInputFocus()

function goFeedback() {
  checkPointEnv('clickHelpCenterHomeButton', {
    button: '意见反馈'
  })
  router.push(`/Feedback${location.search}`)
}

function goConsult() {
  checkPointEnv('clickHelpCenterHomeButton', {
    button: '咨询客服'
  })
  helpPoint.post('getHelpCenterPopup')
  consultVisible.value = true
}

function goConsultOutside() { // 端外环境的咨询客服
  checkPointEnv('clickHelpCenterHomeButton', {
    button: '咨询客服'
  })

  const url = `${import.meta.env.VITE_HOST}/kefu-web-pc/mobile`
  openUrl(url, '_self')
}

const showBtn = ref(false)
const showBtnOutside = ref(false)
function checkVersion() {
  const { appVersion, userId } = usePoint()
  if (!userId && !appVersion) { // 端外环境
    showBtnOutside.value = true
    return
  }

  const appVersionArr = appVersion?.split('.')
  console.log(appVersion)
  if (!appVersionArr) {
    return false
  }

  if ((Number(appVersionArr[0]) === 7) && (Number(appVersionArr[1]) > 47)) {
    showBtn.value = true
  }

  if (Number(appVersionArr[0]) >= 8) {
    showBtn.value = true
  }
}


onMounted(() => {
  checkVersion()
})
</script>

<style scoped lang='scss'>
.bottom-container {
  box-shadow: 0px -2px 15px #ddd;
}
</style>
