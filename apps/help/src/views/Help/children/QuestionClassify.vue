<!--
 * @Date         : 2024-05-14 14:38:35
 * @Description  : 常见问题分类
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="configStore.helpCenterConfig.solutionSet?.length !== 0"
       class="px-42px my-23px">
    <Title title="常见问题分类" />

    <div class="grid grid-cols-3 gap-20px ">
      <div v-for="(item, index) in configStore.helpCenterConfig.solutionSet"
           :key="index"
           class="w-212px h-62px flexD bg-#EEFAFF rounded-12px b-2px b-#40C2FF b-solid cursor-pointer"
           @click="goClassify(item)">
        <span class="c-#2CB9FB text-28px font-400">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import Title from '@/views/Help/components/Title.vue'
import { useConfig } from '@/store/config'
import { useRouter } from 'vue-router'

const router = useRouter()
const configStore = useConfig()

function goClassify(info) {
  configStore.currentClassifyId = info.id
  router.push('/QuestionClassify')
}
</script>

<style scoped lang='scss'>

</style>
