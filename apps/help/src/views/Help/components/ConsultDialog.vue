<!--
 * @Date         : 2023-10-31 16:51:58
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <van-dialog v-model:show="_visible"
              @open="openDialog">
    <template #default>
      <div class="flex items-center justify-end pt-10PX">
        <van-icon name="cross"
                  size="18PX"
                  class="c-#969799 mr-10PX"
                  @click="closeDialog" />
      </div>

      <div class="w-100% flexD py-20PX">
        <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/icon-9cbb0fe066f5d7fd48f92c9e775dc58f__w.png"
                        class="w-180PX" />
      </div>

      <div class="flexD flex-col PX-20PX">
        <div class="mb-20PX text-18PX">{{ serviceBoardStore.title }}</div>
        <div class="c-#969799 text-14PX line-height-20PX mx-30PX">{{ serviceBoardStore.content }}</div>
      </div>
    </template>

    <template #footer>
      <div class="w-100% flex items-center justify-evenly py-20PX">
        <van-button round
                    class="w-120PX h-35PX!"
                    type="primary"
                    @click="goConsult">
          <span class="ml-5PX">在线咨询</span>
        </van-button>

        <!-- <van-button round
                    class="w-120PX h-35PX!"
                    type="success"
                    @click="callTel">
          <span class="ml-5PX">电话咨询</span>
        </van-button> -->
      </div>
    </template>
  </van-dialog>
</template>

<script lang='ts' setup>
import { useModel } from '@/hooks/useBasic'
import helpPoint from '@/script/helpPoint'
import { useServiceBoard } from '@/store/serviceBoard'
import useConsult from '@/hooks/useConsult'
import 'vant/lib/toast/index.css'

const { callTel, goConsult } = useConsult()
const serviceBoardStore = useServiceBoard()

const props = defineProps<{
  visible: boolean
}>()
const emits = defineEmits(['update:visible'])
const _visible = useModel(props, emits, 'visible')



function closeDialog() {
  helpPoint.post('clickHelpCenterCustomerServicePopup', {
    button: '关闭'
  })
  _visible.value = false
}

function openDialog() {

}
</script>

<style scoped lang='scss'>

</style>
