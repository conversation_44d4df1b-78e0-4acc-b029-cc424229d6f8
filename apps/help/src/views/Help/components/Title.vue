<!--
 * @Date         : 2024-05-14 14:30:47
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="mb-18px">
    <div class="flex items-center mb-18px">
      <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/icon_title-5e6cc428a46ec1b89e55e5fb7c41dbb2__w.png"
                      class="w-47px h-44px mr-11px"
                      alt="icon_title" />

      <div class="text-32px font-600 c-black">{{ title }}</div>
    </div>

    <van-divider class="b-#CCEFFF! m-0!" />
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  title: string
}>()
</script>

<style scoped lang='scss'>

</style>
