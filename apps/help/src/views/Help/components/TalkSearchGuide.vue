<!--
 * @Date         : 2023-11-13 11:06:54
 * @Description  : 输入词条检索引导
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div ref="GudieRef"
       class="guide-container rounded-4px absolute bg-white w-100% z-99 w-700px left-25px">
    <div v-if="guideList.length !== 0">
      <div v-for="(item, index) in guideList"
           :key="index"
           class="py-12px px-37px"
           :class="[index !== guideList.length - 1 ? 'b b-b b-b-#ECECEC b-b-solid' : '']">
        <span class="text-28px c-#999 text"
              @click="goDetail(item.docId, '词条检索')"
              v-html="searchHighLight(item.question)" />
      </div>
    </div>

    <div v-if="guideList.length === 0 && haveSearch"
         class="h-120px flexD flex-col">
      <div class="c-#999 mb-3px text-28px">暂无搜索内容</div>
      <div class="c-#4F9FF9 text-26px">您可以换个词试试</div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'
import { SearchGuideItem } from '@/api/modules/help'
import { useModel } from '@/hooks/useBasic'
import useGoDetail from '../hooks/useGoDetail'

const { goDetail } = useGoDetail()
const props = defineProps<{
  text: string
}>()

const emit = defineEmits(['update:text'])
const _text = useModel(props, emit, 'text')

const guideList = ref<SearchGuideItem[]>([])

async function getGuide(text) {
  const res = await api.searchGuide(text)

  if (props.text.length >= 2) {
    guideList.value = res.list
  }
}

const GudieRef = ref()
const haveSearch = ref(false)

watch(() => props.text, (newV) => {
  const val = newV.trim()
  if (val.length >= 2) {
    getGuide(val)
    haveSearch.value = true
  } else {
    guideList.value.length = 0
    haveSearch.value = false
  }
})

function searchHighLight(str:string) {  // 搜索高亮
  // 使用<span>标签标记指定字符
  const targetStr = props.text.replace(/(\++)/g, '\\$1').trim() // 处理+字符，避免+被识别为量词
  let markedString = str.replace(new RegExp(targetStr, 'gi'), function(match) {
    return '<span style="color: #4F9FF9;">' + match + '</span>'
  })

  return markedString
}
</script>

<style scoped lang='scss'>
.text {
  @include textHideLine1;
  width: 560px;
  display: inline-block;
}

.guide-container {
  box-shadow: 0px 5px 10px #ddd;
}
</style>
