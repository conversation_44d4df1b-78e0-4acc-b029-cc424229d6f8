<!--
 * @Date         : 2023-10-31 10:58:02
 * @Description  : 帮助中心
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <NoticeBar />

    <div class="help-container overflow-hidden">
      <div class="mt-170px">
        <Search />
        <div class="h-[calc(100vh-240px-155px)] overflow-auto">
          <!-- <CommonTools /> -->
          <CommonTools v-if="useUser().isInApp" />
          <div class="h-19px bg-#F2F2F2" />
          <HotQuestion />
          <div v-if="configStore.helpCenterConfig.solutionSet?.length !== 0"
               class="h-19px bg-#F2F2F2" />
          <QuestionClassify />
        </div>
      </div>
      <BottomButton />
    </div>
  </div>
</template>

<script setup lang="ts" name="Help">
import {
  browserTitle,
} from '@guanghe-pub/onion-utils'
import { checkPointEnv } from '@/script/helpPoint'
import { useServiceBoard } from '@/store/serviceBoard'
import BottomButton from './children/BottomButton.vue'
import Search from './children/Search.vue'
import HotQuestion from './children/HotQuestion.vue'
import QuestionClassify from './children/QuestionClassify.vue'
import CommonTools from './children/CommonTools.vue'
import { useConfig } from '@/store/config'
import { useUser } from '@/store/user'
import NoticeBar from './children/NoticeBar.vue'

const configStore = useConfig()
onMounted(() => {
  document.title = '帮助中心'
  browserTitle('帮助中心')

  checkPointEnv('getHelpCenterHome')
  useServiceBoard().getServiceBoard()
})

onBeforeMount(() => {
  configStore.getHelpCenterConfig()
})
</script>
<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  max-width: 750px;
}

.app {
  height: 100%;
  padding: 0 22px;
  margin-bottom: calc(60px + var(--safe-area-bottom));

  .issue {
    .issue-hd {
      h3 {
        position: relative;
        padding-left: 36px;
        margin-top: 20px;
        margin-bottom: 0;
        font-size: 16px;
        font-weight: 400;

        .number {
          position: absolute;
          left: 0;
          top: 0;
          line-height: 22px;
          color: #1797EB;
          font-family: PingFangSC-Semibold;
        }

        .double {
          font-size: 14px !important;
        }

        .title {
          color: #2c2c2c;
          text-align: left;
          line-height: 22px;
          font-family: PingFangSC-Semibold;
        }
      }

      .answer {
        font-family: PingFangSC-Regular;
        color: #2c2c2c;
        line-height: 21px;
        font-size: 14px;
        padding: 6px 0 4px 36px;

        p {
          margin: 0;
        }

        .secondTitle {
          font-weight: bold;
        }
      }
    }
  }

  .last-child {
    padding-bottom: 58px;
  }
}

.btn-box {
  padding-top: 10px;
  padding-bottom: calc(10px + var(--safe-area-bottom));
}

.help-container {
  background: url('https://fp.yangcong345.com/middle/1.0.0/bg_help-4b3d9848832844639786d26d4f35e292__w.png') no-repeat;
  background-size: 100%;
  background-color: white;
  height: 100vh;
}
</style>
