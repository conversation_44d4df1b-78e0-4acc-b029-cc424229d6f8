import { useRouter } from 'vue-router'

export default function() {
  const router = useRouter()

  function goDetail(docId: string, from: string) {
    router.push({
      path: '/QuestionDetail',
      query: { docId, from }
    })
  }

  function goDetailNext(docId: string, from: string) {
    router.push({
      path: '/QuestionDetail',
      query: { docId, from }
    })
  }

  return { goDetail, goDetailNext }
}
