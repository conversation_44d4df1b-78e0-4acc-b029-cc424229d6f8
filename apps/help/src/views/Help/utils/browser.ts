const BROWSER = (function() {
  const ua = navigator.userAgent.toLowerCase()
  const isAndroid = ua.indexOf('android') !== -1 ? 1 : 0
  const isiPhone = ua.indexOf('iphone') > -1 || ua.indexOf('mac') > -1
  return {
    isAndroid: isAndroid,
    isiOS: !!ua.match(/\(i[^;]+;( u;)? cpu.+mac os x/),
    isiPhone: isiPhone,
    isiPhoneX: isiPhone && screen.height === 812 && screen.width === 375,
    isiPad: ua.indexOf('ipad') > -1,
    isWeChat: ua.indexOf('micromessenger') !== -1 ? 1 : 0,
    isQQ: !!ua.match(/QQ/i),
    isWindows: ua.indexOf('windows') > -1,
    isWeiBo: !!ua.match(/Weibo/i),
    isAndroidChrome:
      (ua.match(/Chrome\/([\d.]+)/) || ua.match(/CriOS\/([\d.]+)/)) &&
      !!ua.match(/Android/i) &&
      !ua.match(/QQ/i),
    androidVersion: isAndroid ? ua.substr(ua.indexOf('android') + 8, 1) : false,
  }
})()

export default BROWSER
