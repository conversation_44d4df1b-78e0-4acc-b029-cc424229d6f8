<template>
  <div>
    <ul class="image-uploader-list">
      <li v-for="(item, index) in list"
          :key="index"
          class="image-uploader-list-item"
          :style="{
            background: item.status !== 'success' ? 'rgba(0,0,0,0.5)' : '#ffffff'
          }">
        <img v-if="item.status === 'success'"
             :src="item.url"
             @click="() => preViewImage(item.url)">
        <div v-if="item.status === 'uploading'"
             class="image-uploader-status-container">
          <p class="image-uploader-status-text">上传中</p>
          <p class="image-uploader-status-text">{{ item.percentage }}%</p>
        </div>
        <div v-if="false"
             class="image-uploader-status-container">
          <p class="image-uploader-error-icon" />
          <p class="image-uploader-status-text">上传失败</p>
        </div>
        <p class="image-uploader-close-icon"
           @click="() => deleteImage(index)" />
      </li>
      <li v-if="list.length < maxCount"
          class="image-uploader-list-item image-uploader">
        <div class="image-uploader-btn">+</div>
        <input type="file"
               :accept="accept"
               @change="onFileChange">
      </li>
    </ul>
    <div v-if="visible"
         class="image-preview-container"
         @click="() => setVisible()">
      <img :src="activeUrl">
    </div>
  </div>
</template>

<script setup lang="ts">
import { browserShowToast, getHost } from '@guanghe-pub/onion-utils'
import { usePoint } from '@/store/point'
import { pressImg } from '../utils/pressImg'
import ycUpload from '@guanghe-pub/yc-upload'
const { onFileInputChange, httpRequest } = ycUpload

const host = getHost(import.meta.env.VITE_GETHOST)
const props = withDefaults(defineProps<{
  fileList: any[]
  maxCount: number
  accept: any
}>(), {
  fileList: () => [],
  accept: () => ['image/*']
})

const emits = defineEmits(['change'])

const list = ref<any[]>([])
const activeUrl = ref('')
const visible = ref(false)

function onFileChange(event) {
  const validFileType = file => {
    return props.accept.includes(file.type)
  }
  const fileList = event.target && event.target.files
  if (!fileList || !fileList.length) return
  const file = new File([fileList[0]], usePoint().userId + '-' + fileList[0].name, {
    type: fileList[0].type
  })
  pressImg({
    file: file,
    targetSize: 1 * 1024 * 5000,
    quality: 0.5,
    succ: async resultFile => {
      // 如果不是null就是压缩成功
      if (resultFile) {
        const file = resultFile
        if (!validFileType(file)) {
          browserShowToast('请上传正确格式的图片', 2000)
          return
        }
        const env =
              import.meta.env.VITE_GETHOST === 'development'
                ? 'test'
                : import.meta.env.VITE_GETHOST === 'production'
                  ? 'prod'
                  : import.meta.env.VITE_GETHOST

        const e = {
          target: {
            type: 'file',
            files: [file]
          }
        }
        onFileInputChange(e, {
          // 当前运行环境，值为：test | stage | prod，string类型，必填项
          env: env,
          // 上传文件前用来获取一次性上传token的方法，function类型，必填项
          getToken: getToken,
          // 选择文件前的fileList长度
          filesLength: 0,
          // 文件个数限制，number类型
          limit: 1,
          // 文件大小限制，单位：b，number类型
          size: 1 * 1024 * 5000, // 5MB
          fileUploadLimit: 1, // 上传文件最大并发数，默认为4
          fileNameType: 2, // 1：原文件名 2：原文件名+文件hash 3：文件hash（不带文件后缀） 4：文件hash（带文件后缀）, 默认值2
          filePath: 'message-feedback', // 上传的文件夹路径，格式不能包含空格，不能以 '/'开头
          // 文件个数超出限制的钩子
          exceedLimitHandler: () => {
            browserShowToast('文件超出个数限制', 2000)
          },
          // 文件大小超出限制的钩子
          exceedSizeHandler: () => {
            browserShowToast('文件大小超出限制', 2000)
          },
          // 文件上传前的钩子
          beforeUploadFileHandler: f => {
            const file = {
              ...f,
              status: 'ready',
              percentage: 0
            }
            list.value.push(file)
          },
          fileUploadProgressHandler: (e, file) => {
            list.value = list.value.map(item => {
              if (item.uid === file.uid) {
                item.status = file.status
                item.percentage = file.percentage
              }
              return item
            })
          },
          // 单个文件上传成功后的回调钩子
          fileUploadEndHandler: (response, file) => {
            const fileIndex = list.value.findIndex(item => item.uid === file.uid)
            if (fileIndex !== -1) {
              list.value.splice(fileIndex, 1, file)
            }
            emits('change', list.value)
          },
          // 单个文件上传失败的回调钩子
          fileUploadErrorHandler: () => {
            const fileInput:any = document.getElementById('fileInput')
            if (fileInput) {
              fileInput.value = ''
            }
            list.value = []
            emits('change', list.value)
            browserShowToast(`图片上传失败，请重试`, 3000)
            return
          }
        })
      } else {
        browserShowToast('压缩失败', 2000)
      }
    }
  })
}
function preViewImage(url) {
  activeUrl.value = url
  visible.value = true
}
function setVisible(_visible = false) {
  visible.value = _visible
}
function deleteImage(index) {
  list.value.splice(index, 1)
  emits('change', list.value)
}
async function getToken() {
  const res = await httpRequest({
    url: `${host.h5Host}/backend/yc-oss/token?bucket=fp&expires=1800`,
    method: 'post'
  })
  return res?.data?.token
}

onMounted(() => {
  list.value = list.value.concat(props.fileList)
})
</script>
<style lang="scss" scoped>
.image-uploader-list {
  display: flex;
  align-items: center;
  padding: 0;
  list-style: none;
  .image-uploader-list-item {
    position: relative;
    width: 86px;
    height: 86px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #cccccc;
    margin-right: 12px;
    margin-bottom: 12px;
    &.image-uploader {
      display: flex;
      align-items: center;
      justify-content: center;
      .image-uploader-btn {
        color: #e9e9e9;
        font-size: 48px;
      }
      input[type='file'] {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 10;
        opacity: 0;
      }
    }
    img {
      width: 100%;
      height: 100%;
    }
    .image-uploader-close-icon {
      position: absolute;
      top: -4px;
      right: -4px;
      height: -4px;
      width: 16px;
      height: 16px;
      margin: 0;
      background: url('./assets/message-feedback/feedback_image_ic_delete.png');
      background-size: 100%;
      border-radius: 50%;
      overflow: hidden;
    }
    .image-uploader-status-container {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 52px;
      transform: translate(-50%, -50%);
      z-index: 10;
      .image-uploader-error-icon {
        width: 24px;
        height: px2vw(48);
        height: 24px;
        margin: auto;
        background: url('./assets/message-feedback/feedback_image_ic_error.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
      .image-uploader-status-text {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}
.image-preview-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  img {
    width: 100%;
  }
}
</style>
