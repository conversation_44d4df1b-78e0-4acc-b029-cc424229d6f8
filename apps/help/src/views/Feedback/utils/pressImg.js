/**
 * @Date         : 2023-10-31 14:38:28
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-10-31 14:38:29
 */

import EXIF from 'exif-js'
/**
 * @param {二进制文件流} file
 * @param {回调函数，返回base64} fn
 */
function changeFileToBaseURL(file, fn) {
  // 创建读取文件对象
  const fileReader = new FileReader()
  // 如果file没定义返回null
  if (file === undefined) return fn(null)
  // 读取file文件,得到的结果为base64位
  fileReader.readAsDataURL(file)
  fileReader.onload = function() {
    // 把读取到的base64
    const imgBase64Data = this.result
    fn(imgBase64Data)
  }
}
/**
 * 将base64转换为文件
 * @param {baseURL} dataurl
 * @param {文件名称} filename
 * @return {文件二进制流}
 */
function dataURLtoFile(dataurl, filename) {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}
// eslint-disable-next-line max-params
function rotateImg(img, direction, canvas, ctx) {
  // alert(img);
  // 最小与最大旋转方向，图片旋转4次后回到原方向
  const minStep = 0
  const maxStep = 3
  // var img = document.getElementById(pid);
  if (img === null) return
  // img的高度和宽度不能在img元素隐藏后获取，否则会出错
  // var step = img.getAttribute('step');
  const zoom = img.height > 600 ? img.height / 600 : 1
  let maxHeight
  let maxWidth
  // 获得长宽比例
  const scale = img.width / img.height
  if (img.height * scale > img.width) {
    maxHeight = img.width / scale / zoom
    maxWidth = img.width / zoom
  } else {
    maxWidth = (img.height * scale) / zoom
    maxHeight = img.height / zoom
  }
  const height = Math.min(img.height, maxHeight)
  const width = Math.min(img.width, maxWidth)
  let step = 2
  if (step === null) {
    step = minStep
  }
  if (direction === 'right') {
    step++
    // 旋转到原位置，即超过最大值
    step > maxStep && (step = minStep)
  } else {
    step--
    step < minStep && (step = maxStep)
  }
  // img.setAttribute('step', step);
  /* var canvas = document.getElementById('pic_' + pid);
  if (canvas == null) {
      img.style.display = 'none';
      canvas = document.createElement('canvas');
      canvas.setAttribute('id', 'pic_' + pid);
      img.parentNode.appendChild(canvas);
  }  */

  // 旋转角度以弧度值为参数
  const degree = (step * 90 * Math.PI) / 180
  switch (step) {
    case 0:
      canvas.width = width
      canvas.height = height
      ctx.drawImage(img, 0, 0, width, height)
      break
    case 1:
      canvas.width = height
      canvas.height = width
      ctx.rotate(degree)
      ctx.drawImage(img, 0, -height, width, height)
      break
    case 2:
      canvas.width = width
      canvas.height = height
      ctx.rotate(degree)
      ctx.drawImage(img, -width, -height, width, height)
      break
    case 3:
      canvas.width = height
      canvas.height = width
      ctx.rotate(degree)
      ctx.drawImage(img, -width, 0, width, height)
      break
  }
}

/**
 * canvas压缩图片
 * @param {参数obj} param
 * @param {文件二进制流} param.file 必传
 * @param {目标压缩大小} param.targetSize 不传初始赋值-1
 * @param {输出图片宽度} param.width 不传初始赋值-1，等比缩放不用传高度
 * @param {输出图片名称} param.fileName 不传初始赋值image
 * @param {压缩图片程度} param.quality 不传初始赋值0.92。值范围0~1
 * @param {回调函数} param.succ 必传
 */
export function pressImg(param) {
  // 如果没有回调函数就不执行
  if (param && param.succ) {
    // 如果file没定义返回null
    if (param.file === undefined) return param.succ(null)
    // 给参数附初始值
    param.targetSize = param.hasOwnProperty('targetSize') ? param.targetSize : -1
    param.width = param.hasOwnProperty('width') ? param.width : -1
    param.fileName = param.hasOwnProperty('fileName') ? param.fileName : param.file.name || 'image'
    param.quality = param.hasOwnProperty('quality') ? param.quality : 0.92
    // 得到文件类型
    const fileType = param.file.type
    // console.log(fileType) //image/jpeg
    if (fileType.indexOf('image') === -1) {
      console.log('请选择图片文件^_^')
      return param.succ(null)
    }
    // 如果当前size比目标size小，直接输出
    const size = param.file.size
    if (param.targetSize > size) {
      return param.succ(param.file)
    }
    let Orientation = ''
    EXIF.getData(param.file, function() {
      // eslint-disable-next-line no-invalid-this
      Orientation = EXIF.getTag(this, 'Orientation')
    })
    // 读取file文件,得到的结果为base64位
    changeFileToBaseURL(param.file, function(base64) {
      if (base64) {
        const image = new Image()
        image.src = base64
        image.onload = function() {
          // console.log(scale);
          // 创建一个canvas
          const canvas = document.createElement('canvas')
          // 获取上下文
          const context = canvas.getContext('2d')
          const fillStyle = 'transparent'
          context.fillStyle = fillStyle
          if (Orientation === 6 || Orientation === 8 || Orientation === 3) {
            console.log('旋转处理')
            switch (Orientation) {
              case 6: // 需要顺时针（向左）90度旋转
                console.log('需要顺时针（向左）90度旋转')
                rotateImg(this, 'left', canvas, context)
                break
              case 8: // 需要逆时针（向右）90度旋转
                console.log('需要顺时针（向右）90度旋转')
                rotateImg(this, 'right', canvas, context)
                break
              case 3: // 需要180度旋转
                console.log('需要180度旋转')
                rotateImg(this, 'right', canvas, context) // 转两次
                rotateImg(this, 'right', canvas, context)
                break
            }
          } else {
            // 获得长宽比例
            const zoom = this.height / 2 > 600 ? 2 : 1
            let maxHeight
            let maxWidth
            const scale = this.width / this.height
            if (this.height * scale > this.width) {
              maxHeight = this.width / scale / zoom
              maxWidth = this.width / zoom
            } else {
              maxWidth = (this.height * scale) / zoom
              maxHeight = this.height / zoom
            }
            const height = Math.min(this.height, maxHeight)
            const width = Math.min(this.width, maxWidth)
            canvas.width = width
            canvas.height = height
            context.drawImage(this, 0, 0, width, height)
          }
          // 压缩图片，获取到新的base64Url
          const newImageData = canvas.toDataURL(fileType, param.quality)
          // 将base64转化成文件流
          const resultFile = dataURLtoFile(newImageData, param.fileName)
          // 判断如果targetSize有限制且压缩后的图片大小比目标大小大，就弹出错误
          if (param.targetSize !== -1 && param.targetSize < resultFile.size) {
            console.log('图片上传尺寸太大，请重新上传^_^', param.targetSize, resultFile.size)
            param.succ(null)
          } else {
            // 返回文件流
            param.succ(resultFile)
          }
        }
      }
    })
  }
}
