/**
 * @Date         : 2023-10-30 17:45:11
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-10-30 17:45:29
 */

/**
 * check browser type
 */

export function compareVersion(left, right) {
  if (right && right.indexOf('-')) {
    // eslint-disable-next-line no-param-reassign
    right = right.split('-')[0]
  }
  const regex = /^(\d+)\.*(\d*)\.*(\d*)/
  if (regex.test(left) && regex.test(right)) {
    const leftMatch = left.match(regex)
    const rightMatch = right.match(regex)
    const x = [parseInt(leftMatch[1], 10), parseInt(rightMatch[1], 10)]
    const y = [parseInt(leftMatch[2], 10), parseInt(rightMatch[2], 10)]
    const z = [parseInt(leftMatch[3], 10), parseInt(rightMatch[3], 10)]
    if (x[1] !== x[0]) return x[1] > x[0] ? 1 : -1
    if (y[1] !== y[0]) return y[1] > y[0] ? 1 : -1
    if (z[1] !== z[0]) return z[1] > z[0] ? 1 : -1
    return 0
  }
}
const BROWSER = (function() {
  const ua = navigator.userAgent.toLowerCase()
  const isAndroid = ua.indexOf('android') !== -1 ? 1 : 0
  const isiPhone = ua.indexOf('iphone') > -1 || ua.indexOf('mac') > -1
  return {
    isAndroid: isAndroid,
    isiOS: !!ua.match(/\(i[^;]+;( u;)? cpu.+mac os x/),
    isiPhone: isiPhone,
    isiPhoneX: isiPhone && screen.height === 812 && screen.width === 375,
    isiPad: ua.indexOf('ipad') > -1,
    isWeChat: ua.indexOf('micromessenger') !== -1 ? 1 : 0,
    isQQ: !!ua.match(/QQ/i),
    isWindows: ua.indexOf('windows') > -1,
    isWeiBo: !!ua.match(/Weibo/i),
    isAndroidChrome:
      (ua.match(/Chrome\/([\d.]+)/) || ua.match(/CriOS\/([\d.]+)/)) &&
      !!ua.match(/Android/i) &&
      !ua.match(/QQ/i),
    androidVersion: isAndroid ? ua.substr(ua.indexOf('android') + 8, 1) : false,
  }
})()

export default BROWSER
