/**
 * @Date         : 2023-10-30 17:45:11
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-10-30 17:47:13
 */

/*
 * @Author: <PERSON><PERSON>ian
 * @Date: 2020-10-19 15:34:38
 * @LastEditors: HuangQ<PERSON>
 * @LastEditTime: 2020-10-19 16:44:31
 * @Description: file content
 */
import { buryPoint, h5BuryPoint, uuid } from '@guanghe-pub/onion-utils'
import axios from 'axios'

const env = import.meta.env.VITE_ENV

/* eslint-disable no-tabs */
/** 站外公共埋点字端
    产品名称 字段 含义 枚举值
    shareDevice string 设备唯一标示。 分享者设备id
    device string 设备唯一标示。 使用者设备id
    u_user string 用户洋葱id（ 超长字符串， 用户表id） 使用者的id 没有传空字符串
    eventTime bigint 埋点触发时间（ 用户设备时间）
    os string 操作系统 使用者 android、 ios、 windows、 others
    productId string 产品编码 41
    url string 当前h5页面地址
    category string 自定义（ 用于埋点分类描述）
    unionId string 用户微信unionId 如： 13955544215 没有传空字符串
    eventkey string
 *
 */

/**
 * mobileH5自己发埋点
 * @param {string} eventKey 埋点key
 * @param {object} eventValue 埋点字段
 * @param {string} category 埋点类别
 */
export function mBuryPoint(eventKey, eventValue, category = 'site') {
  if (!localStorage.getItem('customSessionId')) {
    localStorage.setItem('customSessionId', uuid())
  }
  eventValue.sessionId = localStorage.getItem('customSessionId')
  const h5Point = h5BuryPoint({ axios, env, log: env !== 'production' })
  h5Point(eventKey, eventValue, category)
}

/**
 * 调用 native 埋点，请在 native 中打开时使用
 *
 * @param {string} eventKey 埋点key
 * @param {object || string} eventValue 埋点字段
 * @param {string} category 埋点类别
 */
export function nBuryPoint(eventKey, eventValue, category = 'site') {
  console.log(eventKey, eventValue, category)
  if (!localStorage.getItem('customSessionId')) {
    localStorage.setItem('customSessionId', uuid())
  }
  if (Object.prototype.toString.call(eventValue) === '[object Object]') {
    eventValue.sessionId = localStorage.getItem('customSessionId')
  }
  // 兼容老的发送埋点只有category情况
  /* eslint-disable */
  if (Object.prototype.toString.call(eventValue) === '[object String]') {
    category = eventValue
    eventValue = {}
  }
  // 正式环境不输出log
  if (!env === 'production') { console.log({ eventKey, eventValue, category }) }
  buryPoint(eventKey, eventValue, category)
}
