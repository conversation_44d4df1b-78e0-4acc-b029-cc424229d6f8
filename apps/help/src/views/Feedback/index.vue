<template>
  <div class="app">
    <Notice v-if="!feedbackType" />

    <div v-if="!feedbackType"
         class="feedback-container"
         style="background: #FFFFFF">
      <div class="feedback-list-title">请选择问题发生的场景：</div>
      <ul class="list-container">
        <li v-for="(item, index) in feedbackTypeList"
            :key="index"
            class="feedback-list-item"
            @click="() => setFeedbackType(item)">
          <div class="feedback-list-item-content">
            <div class="feedback-list-item-content-title">{{ item.value }}</div>
            <div class="feedback-list-item-content-extra">{{ item.extra }}</div>
          </div>
          <div class="feedback-list-item-icon" />
        </li>
      </ul>
    </div>

    <div v-if="feedbackType"
         class="feedback-container"
         style="position: relative;background: #F8F9FA">

      <h2>问题描述（必填）</h2>
      <div class="content">
        <textarea v-model="textContent"
                  :disabled="!canSubmit"
                  class="textarea"
                  maxLength="240"
                  minLength="11"
                  :placeholder="placeholder || '请认真描述问题和建议，方便我们可以更快的帮到你哦～（内容大于5个字）'" />
        <p class="conterNum"
           :class="[textContent.length === 240 ? 'red' : 'default']">
          {{ conterNum }}/240
        </p>
      </div>
      <h2 v-if="!isTeacherApp">问题图片（选填）</h2>
      <ImageUploader v-if="!isTeacherApp"
                     :file-list="fileList"
                     :max-count="1"
                     :accept="allowedFileTypes"
                     @change="changeFileList" />
      <h2>联系方式（选填）</h2>
      <input v-model="customerEmail"
             class="input"
             type="text"
             placeholder="留下联系方式，更可能解决问题～">
      <div class="btn btn-disabled">
        <van-button class="w-100%"
                    type="primary"
                    :class="[textContent.length < 5 ? 'default' : 'active']"
                    :disabled="textContent.length < 5 ? true : false"
                    @click="debouncedSendFeedback">
          提交
        </van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { useDebounceFn } from '@vueuse/core'
import Notice from '@/components/Notice/index.vue'
import {
  getQueryString,
  browserShowToast as showToast,
  browserClose,
} from '@guanghe-pub/onion-utils'
// import { nBuryPoint } from './utils/buryPoint'
import helpPoint from '@/script/helpPoint'
import DOMAIN from './utils/domain'
import ImageUploader from './components/ImageUploader.vue'
import dayjs from 'dayjs'
import { showToast as showToastVant } from 'vant'
import 'vant/es/toast/style'
import { useRouter } from 'vue-router'

const router = useRouter()
const allowedFileTypes = ['image/jpeg', 'image/png']

const placeholder = ref('请认真描述问题和建议，方便我们可以更快的帮到你哦～（内容大于5个字）')
const textContent = ref('')
const customerEmail = ref('')
const feedbackType = ref('')
const feedbackTypeList = ref([
  {
    value: '使用过程中遇到了功能问题',
    extra: '闪退、白屏等功能问题'
  },
  {
    value: '现有课程内容出现问题',
    extra: '课程、习题等内容错误'
  },
  {
    value: '购买支付时遇到了问题',
    extra: '支付及会员权益'
  },
  {
    value: '参与活动遇到了问题',
    extra: '活动规则、奖励'
  },
  {
    value: '对课程更新的建议',
    extra: '催更、催扩科等'
  },
  {
    value: '对现有功能的建议',
    extra: '对洋葱提出的功能建议'
  },
  {
    value: '我还有其他想说的',
    extra: '树洞、倾诉'
  }
])
const fileList = ref<any[]>([])

const conterNum = computed(() => {
  return textContent.value.length
})
const isTeacherApp = computed(() => {
  const teacherApp = getQueryString('appCategory')
  return !!(teacherApp === 'teacher')
})

const debouncedSendFeedback = useDebounceFn(() => {
  sendFeedback()
}, 1000)

function sendFeedback() {
  const userid = getQueryString('userId')
  const appVersion = getQueryString('appVersion')
  const phoneModel = getQueryString('phoneModel')

  if (textContent.value.replace(/(^\s*)|(\s*$)/g, '') === '') {
    showToast('反馈内容不能为空！')
    return
  }
  const data = {
    description: textContent.value, // 问题描述
    source: isTeacherApp.value ? '教师APP' : '学生APP', // 普通用户来源 0 PC,1 微信,2 APP,3 微博,4 WAP 学生 教师 校园
    contact: customerEmail.value || '', // 联系方式
    deviceInfo: phoneModel, // 机型
    clientVersion: appVersion,
    selection: feedbackType.value,
    images: fileList.value.map(file => file.url).join(','),
    userId: userid,
    group: 'suggestion'
  }
  console.log(data)
  axios({
    method: 'POST',
    url: `${DOMAIN.domain}/user-feedback/app/general`,
    data: data
  })
    .then(res => {
      if (res.data) {
        showToast('感谢您所提交的反馈')
        helpPoint.post('clickSCPFSubmit', {
          text: 'submitted'
        })

        // 缓存上次提交时间
        localStorage.setItem('lastSubmitFeedback', String(new Date().getTime()))

        // setTimeout(() => {
        //   browserClose()
        // }, 1000)

        router.push(`/${location.search}`)
      } else {
        showToast('提交失败')
        helpPoint.post('clickSCPFSubmit', {
          text: 'defeat'
        })
      }
    })
    .catch(err => {
      helpPoint.post('clickSCPFSubmit', {
        text: 'defeat'
      })
      const error = (err.response && err.response.data && err.response.data.msg) || '提交失败'
      showToast(error)
      console.error('error ---> ', err)
    })
}

function setFeedbackType(type) {
  feedbackType.value = type.value
  if (feedbackType.value === '对课程更新的建议') {
    placeholder.value = '我们想要了解您具体需要的知识点，请详细说明您的实际年级、学科，以及需要的知识点名称'
  }
}

function changeFileList(list) {
  fileList.value = list
}

const canSubmit = computed(() => {
  const lastSubmitFeedback = localStorage.getItem('lastSubmitFeedback')
  if (!lastSubmitFeedback) return true
  const target = dayjs().subtract(1, 'h').isAfter((dayjs(Number(lastSubmitFeedback))))
  console.log(target, dayjs().subtract(1, 'h'), dayjs(Number(lastSubmitFeedback)))
  if (!target) {
    showToastVant({
      message: '您提交的太频繁啦～请稍后再试',
      duration: 2000,
    })
  }
  return target
})


</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
body,
html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #f4f4f4;
}
.app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .feedback-container {
    // height: 100vh;
    padding: 22px 20px;
    // overflow: auto;
  }
  h2 {
    margin: 12px 0;
    line-height: 12px;
    font-size: 14px;
    color: #222222;
    font-weight: 500;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .feedback-list-title {
    margin-bottom: 12px;
    font-weight: 400;
    color: #999999;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
  }
  .container {
    padding: 0;
  }
  .feedback-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    padding-right: 14px;
    margin-bottom: 14px;
    border-radius: 6px;
    border: 1px solid #cccccc;
    background: #ffffff;
    .feedback-list-item-content-title {
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #222222;
    }
    .feedback-list-item-content-extra {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      font-family: PingFangSC-Regular, PingFang SC;
    }
    .feedback-list-item-icon {
      width: 18px;
      height: px2vw(36);
      height: 18px;
      background: url('./assets/feedback_ic_into.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .content {
    width: 100%;
    position: relative;
    background: #ffffff;
    .textarea {
      width: 100%;
      height: 150px;
      padding: 10px 16px 0;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      font-size: 14px;
      resize: none;
      border: none;
      color: #222222;
      letter-spacing: 0;
      line-height: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;

      &::-webkit-input-placeholder {
        color: #cccccc;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .conterNum {
      position: absolute;
      right: 0;
      top: -48px;
      font-size: 14px;
      line-height: 11px;
      font-family: PingFangSC-Regular, PingFang SC;
      &.default {
        color: #cccccc;
      }
      &.red {
        color: #e24217;
      }
    }
  }

  .input {
    width: 100%;
    padding: 10px 16px;
    margin-bottom: 262px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: px2vw(44);
    line-height: 22px;
    border: none;

    &::-webkit-input-placeholder {
      color: #cccccc;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .btn {
    position: fixed;
    left: 12px;
    right: 12px;
    bottom: 22px;

    .button {
      width: 100%;
      height: 44px;
      border-radius: 8px;
      font-size: 16px;
      line-height: 44px;
      font-family: PingFangSC-Semibold;
      text-align: center;

      &.default {
        background: #3b6ad9;
        opacity: 0.5;
        color: #ffffff;
        box-shadow: 0px 1px 3px 0px rgba(255, 255, 255, 0.5), 0px -2px 3px 0px #3b6ad9;
      }

      &.active {
        background: #518aff;
        box-shadow: 0px 1px 3px 0px rgba(255, 255, 255, 0.5), 0px -2px 3px 0px #3b6ad9;
        color: #ffffff;
      }
    }
  }
}

.btn-disabled {
  :deep(.van-button--disabled) {
    opacity: 1;
    background-color: #a0cfff;
    color: white;
    border-color: #a0cfff;
  }
}
</style>
