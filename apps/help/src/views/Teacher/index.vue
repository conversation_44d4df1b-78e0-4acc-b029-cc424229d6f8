<!--
 * @Date         : 2023-11-21 16:13:02
 * @Description  : 教师端
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2024-10-09 18:52:09
-->

<template>
  <div class="min-h-100vh flex items-center justify-center flex-col">
    <div class="w-100% flexD py-20PX">
      <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/icon-9cbb0fe066f5d7fd48f92c9e775dc58f__w.png"
                      class="w-180PX" />
    </div>

    <div class="flexD flex-col px-20PX">
      <div class="mb-20PX text-18PX">{{ serviceBoardStore.title }}</div>
      <div class="c-#969799 text-14PX line-height-20PX px-50PX">{{ serviceBoardStore.content }}</div>
    </div>

    <div class="w-100% flex items-center justify-evenly py-20PX">
      <van-button round
                  class="w-120PX h-35PX!"
                  type="primary"
                  @click="goConsultTeacher">
        <span class="ml-5PX">在线咨询</span>
      </van-button>

      <!-- <van-button round
                  class="w-120PX h-35PX!"
                  type="success"
                  @click="callTel">
        <span class="ml-5PX">电话咨询</span>
      </van-button> -->
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useServiceBoard } from '@/store/serviceBoard'
import useConsult from '@/hooks/useConsult'
import 'vant/lib/toast/index.css'

const { callTel, goConsultTeacher } = useConsult()
const serviceBoardStore = useServiceBoard()

onMounted(() => {
  useServiceBoard().getServiceBoard()
})
</script>

<style scoped lang='scss'>

</style>
