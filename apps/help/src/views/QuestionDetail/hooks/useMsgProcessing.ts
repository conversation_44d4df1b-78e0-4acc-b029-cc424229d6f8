import { browserJump } from '@guanghe-pub/onion-utils'
import { useConfig } from '@/store/config'
import { useUser } from '@/store/user'
import { replaceUrlHost } from '@kefuweb/utils'

export default function (detail) {
  onBeforeMount(() => {
    useConfig().detailImgList = []
    window.browserJump = browserJump
  })

  const computedAnswer = computed(() => {
    if (detail.value.answer) {
      let result = detail.value.answer
      result = coverHTMLTagImgToElImage(result)
      result = restoreMatchTags(result)

      result = modifyString(result)
      result = replaceUrlHost(result)
      result = matchMiddleUrl(result)
      return result
    }
    return detail.value.answer
  })

  function modifyString(originalString) {
    // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
    let tempDiv = document.createElement('div')
    tempDiv.innerHTML = originalString

    // 获取所有的 <a> 元素
    let linkElements = tempDiv.querySelectorAll('a')

    linkElements.forEach((linkElement) => {
      const href = linkElement.getAttribute('href')
      linkElement.style.color = '#1989fa'
      linkElement.removeAttribute('target')
      if (useConfig().checkPortalList(href)) { // 判断是否为传送门
        if (useConfig().showPortal) {
          const browserJumpParam = {
            url: useUser().splitPortal(href),
            business: { fromPageName: 'kf-help', from: 'kf-help' }
          }
          linkElement.setAttribute('onClick', `browserJump(${JSON.stringify(browserJumpParam)})`)
          linkElement.removeAttribute('href')
        } else {
          linkElement.remove()
        }
      }
    })

    // 获取修改后的字符串
    let modifiedString = tempDiv.innerHTML

    return modifiedString
  }

  const matchList:any = []

  function coverHTMLTagImgToElImage(str) {
    const tagReg =  /<img\s+[^>]*src="([^"]*)"[^>]*style="([^"]*)"[^>]*>/gi
    return str.replace(tagReg, (match, src, style) => {

      useConfig().detailImgList.push(src)

      const target = `<img src="${src}" onClick="window.showImagePreview('${src}')" style="${style}" />`
      return saveMatch(target)
    })
  }

  function saveMatch(match) {
    const tagIndex = matchList.length
    matchList.push(match)
    return `@@${tagIndex}@@`
  }


  function restoreMatchTags(str) {
    return str.replace(/@@(\d+)@@/g, (match, tagIndex) => {
      return matchList[tagIndex] || ''
    })
  }

  function matchMiddleUrl(str) { // 过滤中间页的a标签
    if (useConfig().showMiddleJumptelsale) {
      return str
    }

    // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
    let tempDiv = document.createElement('div')
    tempDiv.innerHTML = str

    // 获取所有的 <a> 元素
    let linkElements = tempDiv.querySelectorAll('a')

    linkElements.forEach(item => {
      const href = item.getAttribute('href')
      if (href?.includes('middle/jumptelsale')) {
        item.remove()
      }
    })

    // 获取修改后的字符串
    let modifiedString = tempDiv.innerHTML

    return modifiedString
  }

  return { computedAnswer }
}

