<!--
 * @Date         : 2023-12-01 18:26:49
 * @Description  : 问题详情页
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2024-10-09 18:51:41
-->

<template>
  <div class="overflow-auto bg-#F2F9FE min-h-100vh w-100vw">
    <div class="px-45px fixed top-0 overflow-auto max-h-100vh w-100vw box-border z-9">
      <div class="c-#419BFF text-30px mb-20px pt-30px">
        Q: {{ detail.baseQuestion }}
      </div>

      <div class="c-#333 text-28px line-height-40px mb-70px">
        A:
        <span class="answer-box"
              v-html="computedAnswer" />
      </div>

      <div v-if="detail.relatedQandas.length !== 0"
           class="bg-white px-30px py-10px rounded-16px">
        <div class="text-30px c-#333 b-b b-b-solid b-b-#ECECEC font-500 line-height-42px pb-10px">{{ detail.relatedGuideSentence }}</div>
        <div>
          <div v-for="(qanda, index) in detail.relatedQandas"
               :key="index"
               :class="[index < detail.relatedQandas.length - 1 ? 'b-b b-b-solid b-b-#E1F2FF' : '']"
               class="py-10px flex items-center justify-between"
               @click="changeDoc(qanda.docId)">
            <van-text-ellipsis class="c-#4F9FF9 w-600px"
                               :content="qanda.qandaBaseQuestion" />
            <van-icon name="arrow"
                      size="14"
                      color="#4F9FF9" />
          </div>
        </div>
      </div>
    </div>

    <div>
      <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/bg_detail-ca17c030d211c346f0abfbef9379a335__w.png"
                      class="fixed top-0 right-0 w-134px" />
      <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/help/bg_detail-ca17c030d211c346f0abfbef9379a335__w.png"
                      class="fixed bottom-31px left-0 w-134px transform-rotate-180deg" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'
import { useRoute } from 'vue-router'
import { QuestionDetail } from '@/api/modules/help'
import { checkPointEnv } from '@/script/helpPoint'
import { showLoadingToast } from 'vant'
import 'vant/es/toast/style'
import useMsgProcessing from './hooks/useMsgProcessing'

const route = useRoute()

const detail = ref<QuestionDetail>({
  docId: '',
  baseQuestion: '',
  answer: '',
  relatedGuideSentence: '',
  qandaQuestions: [],
  relatedQandas: [],
  qandaUniqueId: ''
})

const { computedAnswer } = useMsgProcessing(detail)

async function getInfo(docId: string) {
  const loadingToast = showLoadingToast({
    message: '加载中...'
  })
  const res = await api.getQuestionDetail(docId)
  detail.value = res

  loadingToast.close()
}

function point(from) {
  checkPointEnv('getHelpCenterAnswerPage', {
    actId: from
  })
}

function changeDoc(docId: string) {
  getInfo(docId)
  point('关联问题')
}

onBeforeMount(() => {
  const docId:any = route.query.docId
  const from:any = route.query?.from ?? ''
  getInfo(docId)
  point(from)
})
</script>

<style scoped lang='scss'>
.answer-box {
  :deep(img) {
    max-width: 100%;
  }
}
</style>
