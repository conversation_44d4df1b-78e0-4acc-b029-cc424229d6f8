import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router'
import pinia from '@/store'

import Plugins from '@/plugins'
import '@guanghe-pub/onion-utils'

import 'virtual:svg-icons-register'
import 'virtual:uno.css'
import '@unocss/reset/normalize.css'

import { showImagePreview } from 'vant'
window.showImagePreview = showImagePreview

const app = createApp(App)

app.use(Plugins)
app.use(router)
app.use(pinia)
app.mount('#app')
