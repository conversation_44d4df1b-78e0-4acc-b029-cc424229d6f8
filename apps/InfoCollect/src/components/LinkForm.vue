<!--
 * @Date         : 2024-08-02 17:57:59
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->


<template>
  <div>
    <div v-if="!submitDone">
      <el-form ref="FormRef"
               :model="form"
               label-position="top"
               @submit.prevent>
        <el-form-item label="来电手机号"
                      prop="phone"
                      :rules="[{required: true, message: '请填写来电手机号', trigger: 'blur' }, {validator: validatePhone, trigger: 'blur'}]">
          <el-input v-model="form.phone"
                    maxlength="11"
                    show-word-limit
                    placeholder="请输入手机号"
                    @input="changePhone" />
        </el-form-item>

        <el-form-item label="截图/视频"
                      prop="files"
                      :rules="[{required: true, message: '请上传截图/视频', trigger: 'blur' }]">
          <Update v-model="form.files"
                  v-model:is-upadting="isUpadting" />
        </el-form-item>
      </el-form>

      <el-button :loading="isUpadting || submiting"
                 type="primary"
                 class="w-100%"
                 @click="submit">提交</el-button>
    </div>

    <el-result v-if="submitDone"
               icon="success"
               title="提交完成" />
  </div>
</template>

<script lang='ts' setup>
import Update from '@/components/Update.vue'
import * as api from '@/api'

const FormRef = ref()
const isUpadting = ref(false)
const form = reactive({
  phone: '',
  files: []
})

const validatePhone = (rule: any, value: any, callback: any) => {
  // const phoneReg = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/
  // 正则校验11位数字
  if (value.length !== 11) {
    callback(new Error('请输入11位数字'))
  }
  if (!value) {
    callback(new Error('请输入手机号'))
  }
  // if (!phoneReg.test(value)) {
  //   callback(new Error('请输入正确格式的手机号'))
  // }
  callback()
}

const submitDone = ref(false)
const submiting = ref(false)
async function submit() {
  await FormRef.value.validate()

  submiting.value = true
  try {
    await api.submit({
      phone: String(form.phone),
      links: form.files.map((item: any) => item.url)
    })

    ElMessage.success('提交成功')
    submitDone.value = true
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  }
  submiting.value = false
}

function changePhone(val) {
  // 移除字符串中非数字字符
  val = val.replace(/\D/g, '')
  form.phone = val
}
</script>

<style scoped lang='scss'>

</style>
