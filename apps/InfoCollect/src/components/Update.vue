<!--
 * @Date         : 2024-08-02 12:03:35
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <el-upload ref="UploadRef"
               v-model:file-list="fileList"
               :disabled="fileList.length === limit"
               :accept="accept"
               :limit="limit"
               class="avatar-uploader"
               list-type="picture-card"
               :show-file-list="true"
               :on-change="changeFile"
               :auto-upload="false">

      <el-icon class="avatar-uploader-icon"
               :disabled="fileList.length === limit"><Plus /></el-icon>

      <template #file="{file}">
        <div class="w-100% h-100% relative">
          <div class="absolute right-0 z-99"
               @click="delFile(file)">
            <el-icon color="#F56C6C"
                     size="24px"><CircleCloseFilled /></el-icon>
          </div>

          <div v-if="file.raw.type.includes('video')"
               class="absolute right-50px z-99">
            <el-icon size="24px"
                     color="#303133"><Film /></el-icon>
          </div>
          <el-image v-loading="!filesSrc?.find(item => item.uid === file.uid)"
                    :src="file.url"
                    :initial-index="fileList.findIndex(item => item.uid === file.uid)"
                    :preview-src-list="fileUrlList"
                    class="w-100% h-100%"
                    fit="cover" />
        </div>
      </template>

      <template #tip>
        <div class="el-upload__tip">
          <div>请上传体积小于{{ `${maxSizeM}M` }}</div>
          <div>格式为{{ accept }}的图片/视频</div>
          <div>请上传图片/视频小于{{ limit }}条</div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script lang='ts' setup>
import { type UploadUserFile } from 'element-plus'
import { defineModel } from 'vue'
import { Plus, CircleCloseFilled, Film } from '@element-plus/icons-vue'
import { cutVideoScreenshot } from '@guanghe-pub/nexus-utils'
import * as api from '@/api'

const maxSizeM = 200
const accept = '.mp4,.jpg,.jpeg,.png,.gif'
const limit = 10

const UploadRef = ref()
const fileList = ref<UploadUserFile[]>([])
const fileUrlList = computed(() => {
  return fileList.value.map(file => file.url)
})

const isUpadting = defineModel<boolean>('isUpadting')
const filesSrc = defineModel<any[]>()

async function getVideoInfo(uploadFile) {
  const videoScreen:any = await cutVideoScreenshot(uploadFile.url, 1)
  fileList.value.find(file => file.uid === uploadFile.uid)!.url = videoScreen
}

function getImgInfo(uploadFile) {
  const url = URL.createObjectURL(uploadFile.raw!)
}

async function changeFile(uploadFile) {
  if (uploadFile.size > maxSizeM * 1024 * 1024) {
    ElMessage.error(`图片/视频文件内容过大，请调整后重新上传～`)
    UploadRef.value.handleRemove(uploadFile)
    return
  }
  if (!accept.split(',').some(type => uploadFile.name.includes(type))) {
    ElMessage.error('暂不支持此文件类型上传～')
    UploadRef.value.handleRemove(uploadFile)
    return
  }


  if (uploadFile.raw.type.includes('video')) {
    getVideoInfo(uploadFile)
  } else {
    getImgInfo(uploadFile)
  }

  isUpadting.value = true

  try {
    const { token } = await api.getOssToken()

    const newName = [uploadFile.uid, uploadFile.name.split('.').slice(-1)].join('.')
    const raw = new File([uploadFile.raw], newName, { type: uploadFile.raw.type })

    const res = await api.uploadFile({
      file: raw
    }, token)

    filesSrc.value!.push({
      uid: uploadFile.uid,
      url: `https://wuhan-public.bj.bcebos.com/${res.data.key}`,
      name: uploadFile.name,
    })

  } catch (error) {
    UploadRef.value.handleRemove(uploadFile)
    ElMessage.error('上传失败，请重试')
  }

  if (filesSrc.value?.length === fileList.value.length) isUpadting.value = false
}

function delFile(file) {
  UploadRef.value.handleRemove(file)
  filesSrc.value!.splice(filesSrc.value!.findIndex((f:any) => file.uid === f.uid), 1)
}

onMounted(() => {
  nextTick(() => {
    document.querySelector('.el-upload--picture-card')?.addEventListener('click', () => {
      if (fileList.value.length >= limit) {
        ElMessage.error(`最多上传${limit}张图片/视频`)
        return
      }
    })
  })
})

function initAvatarFile(file) {
  fileList.value = [file]
}

defineExpose({ initAvatarFile })
</script>

<style scoped lang='scss'>

</style>
