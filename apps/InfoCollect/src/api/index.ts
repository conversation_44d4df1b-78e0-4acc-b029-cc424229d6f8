import Axios from './axios'

/**
 * @description: 文件上传
 */
export function uploadFile(par: {
  file: File
  path?: string
}, token: string): Promise<{
  code: number
  msg: string
  debug: string
  data: {
    key: string
    domain: string
    isThirdPartPrivate: boolean
  }
}> {
  const formData = new FormData()
  formData.append('file', par.file)
  return Axios.upload(`${import.meta.env.VITE_HOST_OSS}token/upload`, formData, {
    timeout: 9999999999,
    headers: {
      Authorization: token,
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * @description: 提交表单
 */
export function submit(par: {
  phone: string
  links: string[]
}) {
  return Axios.put('/web/bitable', par)
}

/**
 * @description: 获取oss token
 */
export function getOssToken(): Promise<{
  token: string
}> {
  return Axios.get('/web/oss/token')
}
