import { defineConfig } from 'vite'
import ViteBase from '../../config/vite/src/index.ts'
import { resolve } from 'path'

export default defineConfig(({ mode }) => {
  const baseUrl = '/kefu-web-pc/mobile'

  const config:any = ViteBase({ mode, baseUrl })
  const cwd = process.cwd()

  config.resolve.alias = {
    '@': resolve(cwd, '../pc/src'),
    '_': resolve(cwd, 'src'),
    '#': resolve(cwd, '../../')
  }

  return config
})
