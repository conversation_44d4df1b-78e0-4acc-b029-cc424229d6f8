<!--
 * @Date         : 2023-07-18 16:12:10
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="!userStore.fromPageNameError"
       class="mobile-container">
    <van-pull-refresh id="msgBox"
                      ref="msgBox"
                      v-model="historyLoading"
                      class="overflow-auto!"
                      @refresh="getHistory">

      <div class="pb-10px">
        <div v-if="!historyLoading"
             class="w-100% flexD mt-10px">
          <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/assets/pull_refresh-5615a993724c9a68ec5c1cfb2797b6ea__w.png"
                          class="w-17px" />
        </div>

        <MsgBox :send-tmp-msg="sendTmpMsg" />

      </div>

    </van-pull-refresh>

    <div class="w-100% h-100px footer-box flex justify-center items-end fixed bottom-0 z-9">
      <div v-if="baseStore.networkTimeout"
           class="flexD w-100% py-10px">
        <el-button class="reconnect-btn-mobile"
                   round
                   @click="baseStore.webReload()">
          <el-icon color="#2CABFB"
                   size="20px"><Refresh /></el-icon>
          <span class="c-#666 text-14px">刷新页面</span>
        </el-button>
      </div>

      <div v-if="(baseStore.timeout || baseStore.renew) && !baseStore.networkTimeout"
           class="flexD w-100% py-10px">
        <el-button :loading="baseStore.connectLoading"
                   class="reconnect-btn-mobile"
                   round
                   @click="reconnect">
          <el-icon color="#2CABFB"
                   size="20px"><ChatDotRound /></el-icon>
          <span class="c-#666 text-14px">{{ baseStore.connectLoading ? '重连中': '继续咨询' }}</span>
        </el-button>
      </div>

      <div v-if="!baseStore.timeout && !baseStore.renew && !baseStore.networkTimeout"
           class="w-100%">
        <MenuBar class="mb-11px" />


        <div class="footer flexD relative">
          <TalkSearchGuide v-model:text="textarea"
                           type="mobile"
                           :send-msg="sendTmpMsg" />

          <el-input ref="msgTextareaRef"
                    v-model="textarea"
                    maxlength="100"
                    show-word-limit
                    class="msg-input"
                    placeholder="请简短描述你的问题"
                    @blur="scrollBottom" />


          <el-button round
                     color="#2CABFB"
                     class="text-13px c-white!"
                     :disabled="!baseStore.initCompleted"
                     @click="sendAiMsg">发送</el-button>
        </div>
      </div>
    </div>

    <ErrorDialog />
    <AdvisoryDialog />
  </div>

  <FromPageNameError v-if="userStore.fromPageNameError" />
</template>

<script lang='ts' setup>
import { ChatDotRound, Refresh } from '@element-plus/icons-vue'
import { useBase } from '@/store/base'
import { useUser } from '@/store/user'

import useMqtt from '@/hooks/useMqtt'
import useGetMsg from '@/hooks/useGetMsg'
import useSendMsg from '@/hooks/useSendMsg'
import useHistory from '@/hooks/useHistory'
import useInit from '@/hooks/useInit'
import useStyle from '_/hooks/useStyle'

import MsgBox from '_/components/MsgBox/index.vue'
import MenuBar from '_/components/MenuBar/index.vue'
import ErrorDialog from '@/components/ErrorDialog/mobile.vue'
import TalkSearchGuide from '@/components/TalkSearchGuide/index.vue'
import AdvisoryDialog from '_/components/AdvisoryDialog/index.vue'

import FromPageNameError from '@/components/FromPageNameError/index.vue'

const baseStore = useBase()
const userStore = useUser()

const msgTextareaRef = ref()
const textarea = ref('')

// 滚动到最下方
function scrollBottom() {
  nextTick(() => {
    const msgBox = document.querySelector('#msgBox')
    if (msgBox) {
      msgBox.scrollTop = msgBox?.scrollHeight
    }
  })
}


const { sendTmpMsg, sendAiMsg } = useSendMsg({ textarea, scrollBottom })
const { getHistory, historyLoading } = useHistory()

const { getMsg } = useGetMsg({ scrollBottom })
const { setMqtt, setHeart, getConnect, reconnect } = useMqtt(getMsg)

useInit({ setMqtt, setHeart, getConnect, scrollBottom })

const { vh } = useStyle()

</script>

<style scoped lang='scss'>
.mobile-container {
  background: url('https://fp.yangcong345.com/middle/1.0.0/assets/bg-38cc55e4040c1a81541e29c0059d26cf__w.png') no-repeat;
  background-size: cover;
  background-color: #e1f4ff;
  height: 100vh;
}

.footer {
  padding-right: 10px;
  background: white;

  .msg-input {
    :deep(.el-input__wrapper) {
      padding-top: 10px;
      padding-bottom: 10px;
      border-radius: 0;
      box-shadow: none;
      // ::placeholder {
      //   color: #A6D3F6 !important;
      // }
    }
    :deep(.el-input__inner) {
      background: #F2F2F2;
      padding: 2px 60px 2px 10px;
      border-radius: 14px;
    }
  }

  :deep(.el-input__suffix) {
    position: absolute;
    right: 15px;
    .el-input__count {
      margin-left: 0;
    }
    .el-input__count-inner {
      padding-left: 0;
      background: #F2F2F2;
      color: #999;
    }
  }
}

.reconnect-btn-mobile {
  background: white;
  border: 0;
  width: 90%;
  font-size: 14px;
  color: #666;
  padding: 20px;
}


/* 适配ios底部小黑条 */
.footer-box {
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
  background-color: unset;
}

#msgBox {
  :deep(.van-pull-refresh__track) {
    height: calc(100% - 10px)
  }
  margin-bottom: calc(100px + constant(safe-area-inset-bottom));
  margin-bottom: calc(100px + env(safe-area-inset-bottom));

  height: calc(v-bind(vh) - 100px - env(safe-area-inset-bottom));
}
</style>

<style>
body {
  overflow: hidden;
}
</style>
