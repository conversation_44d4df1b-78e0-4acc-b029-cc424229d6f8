import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Mobile',
    component: () => import('_/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('_/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/mobile',
    name: 'Mobile',
    component: () => import('_/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
]

const router = createRouter({
  history: createWebHistory(''),
  routes,
})

router.beforeEach(beforeEach)

export default router
