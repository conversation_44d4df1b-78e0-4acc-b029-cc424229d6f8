<!--
 * @Date         : 2024-03-29 12:12:05
 * @Description  : 咨询弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <van-popup v-model:show="laborStore.showTelsalePop"
               class="rounded-14px"
               closeable>
      <div class="w-310px h-252px">
        <div class="c-#333 text-17px line-height-24px font-bold mt-18px ml-16px mb-9px">请选择咨询类型</div>
        <div class="c-#333 text-14px line-height-19px mb-22px px-31px">
          为了更加高效解决您的问题，请选择您需要咨询的问题类型
        </div>

        <div class="content-box">
          <div @click="goMiddle">
            <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/assets/AdvisoryDialog/courses-5bdf1c00fbbe9627fe3e82c4b9d36098__w.png"
                            alt="购课咨询" />
            <span>购课咨询</span>
          </div>

          <div @click="goLabor();laborStore.showTelsalePop=false">
            <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/assets/AdvisoryDialog/business-440e304ff381f25c59e371de98f5c7c2__w.png"
                            alt="业务咨询" />
            <span>业务咨询</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang='ts' setup>
import { useLabor } from '@/store/labor'
import { goMiddle, goLabor } from '@/components/AdvisoryDialog/jump'

const laborStore = useLabor()
</script>

<style scoped lang='scss'>
.content-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  >div {
    width: 96px;
    height: 110px;
    border-radius: 5px;
    border: 1px solid #297BF1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    :deep(img) {
      width: 55px;
      height: 56px;
    }
    span {
      color: #297BF1;
      font-size: 14px;
    }
  }
}
</style>
