<template>
  <div>

    <div class="mb-10px">
      <Msg :msg-list="msgStore.historyList"
           :send-tmp-msg="sendTmpMsg" />
    </div>

    <div v-if="!envStore.isPad"
         class="w-100% flexD">
      <Swiper />
    </div>

    <!-- 猜你想问 -->
    <div class="mt-10px px-20px">
      <Guess :send-tmp-msg="sendTmpMsg"
             :check-link-disable="checkLinkDisable" />
    </div>

    <div class="my-10px">
      <Greeting />
    </div>

    <Msg :msg-list="msgStore.list"
         :send-tmp-msg="sendTmpMsg" />

    <!-- 发送AI消息到返回消息之间的loading 流式会返回多次 -->
    <div v-if="baseStore.waitMsgLoading"
         class="justify-start w-100% flex my-10px">
      <div class="max-w-80% flex flex-col ml-20px items-start">
        <div class="p-10px txt-other">
          <MsgLoading />
        </div>
      </div>
    </div>

    <div v-if="baseStore.streamLoading || baseStore.loading"
         class="ml-20px">
      <div class="c-#333333 flexD text-13px ml-5px mt-5px w-fit!">正在输入中...</div>
    </div>

    <div v-if="baseStore.timeout"
         class="text-12px text-center my-10px w-100%">
      <el-text type="info">{{ baseStore.expireSentence }}</el-text>
    </div>

    <div v-if="baseStore.renew"
         class="text-12px text-center my-10px w-100%">
      <el-text type="info">{{ baseStore.renewStr }}</el-text>
    </div>

  </div>
</template>

<script lang='ts' setup>
import { useBase } from '@/store/base'
import MsgLoading from '@/components/MsgLoading/index.vue'

import Greeting from './children/Greeting/index.vue'
import Guess from './children/Guess/index.vue'
import { useMessage } from '@/store/message'
import Msg from './Msg.vue'
import Swiper from '_/components/Swiper/index.vue'
import { useEnv } from '@/store/env'

const envStore = useEnv()
const msgStore = useMessage()

const props = defineProps(['sendTmpMsg'])

const baseStore = useBase()

function checkLinkDisable(info) {
  return baseStore.timeout || baseStore.renew || info.history
}
</script>

<style scoped lang='scss'>
@import url('./txt.scss');
</style>

