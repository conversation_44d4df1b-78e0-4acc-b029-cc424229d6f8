<!--
 * @Date         : 2024-05-11 17:20:24
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="classifypop-container">
    <van-popup v-model:show="show"
               position="bottom"
               safe-area-inset-bottom
               teleport="body"
               class="h-70vh classifypop"
               @open="openPop">
      <div class="flexD h-100%">
        <div class="h-100% flex flex-col w-128px bg-#F1FBFF">
          <div v-for="(item, index) in info"
               :key="index"
               :class="{sideActive: sideActive===index}"
               class="sidebar flexD w-100% h-63px"
               @click="sideActive = index">
            <span class="c-#3B3A4A text-16px font-600">{{ item.solutionSet[0].name }}</span>
          </div>
        </div>


        <div class="flex-1 h-100% overflow-auto">
          <div v-for="(item, index) in info[sideActive].solutionSet[0].qandaSet"
               :key="index"
               class="flex items-center justify-start px-18px pt-24px"
               @click="chooseQuestion(item)">
            <span class="c-#3B3A4A text-16px font-400">{{ item.baseQuestion }}</span>
          </div>
        </div>
      </div>
    </van-popup>

    <QuestionDetailPop v-model="showDetail"
                       :doc-id="docId" />
  </div>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import QuestionDetailPop from './QuestionDetailPop.vue'
import bannerPoint from '../point'

const props = defineProps<{
  current: any
  info: any
}>()

const show = defineModel<boolean>()
const showDetail = ref(false)
const sideActive = ref(0)
const docId = ref('')

function chooseQuestion(item) {
  docId.value = item.docId
  showDetail.value = true
}

function openPop() {
  bannerPoint.h5Post('getCustomerServiceIMBannerAnswerPage')
  sideActive.value = props.info.findIndex((item) => item.solutionSet[0].id === props.current)
}
</script>

<style scoped lang='scss'>
.sideActive {
  background: white;
  position: relative;
  span {
    color: #2CABFB;
  }
  ::before {
    content: '';
    width: 4px;
    height: 24px;
    background: #2CABFB;
    position: absolute;
    left: 0;
    top: calc(50% - 12px);
  }
}
</style>
