<!--
 * @Date         : 2024-05-11 18:47:26
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <van-popup v-model:show="show"
               position="bottom"
               safe-area-inset-bottom
               teleport="body"
               class="h-70vh"
               @open="openPop">
      <div class="h-100% p-24px bg-#F1FBFF">

        <div class="c-#2CABFB text-17px mb-24px">
          Q: {{ question }}
        </div>

        <div class="c-#3B3A4A font-400 text-16px line-height-25px">
          A:
          <span class="answer-box"
                v-html="answer" />
        </div>

      </div>
    </van-popup>
  </div>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import api from '@/api'
import bannerPoint from '../point'

const show = defineModel<boolean>()
const props = defineProps<{
  docId: string
}>()

const question = ref('')
const answer = ref('')

async function openPop() {
  bannerPoint.h5Post('getCustomerServiceIMBannereventPage')
  const res = await api.getQuestionDetail(props.docId)
  question.value = res.baseQuestion
  answer.value = res.answer
}
</script>

<style scoped lang='scss'>

</style>
