import { BuryPoint } from '@guanghe-pub/onion-utils'
import axios from 'axios'

const points = {
  clickCustomerServiceIMBanner: {
    category: 'activity',
    desc: '点击智能客服活动banner',
  },
  getCustomerServiceIMBannereventPage: {
    category: 'activity',
    desc: '智能客服banner的活动明细曝光',
  },
  getCustomerServiceIMBannerAnswerPage: {
    category: 'activity',
    desc: '智能客服banner的答案页曝光',
  },
}

export default new BuryPoint(points, {
  contextData: {
    productId: '419'
  },
  env: import.meta.env.VITE_GETHOST,
  axios
})
