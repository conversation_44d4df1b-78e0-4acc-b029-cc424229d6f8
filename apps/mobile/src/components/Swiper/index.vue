<!--
 * @Date         : 2024-05-11 15:33:26
 * @Description  : 轮播banner
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div id="swiperContainer"
       ref="swiperContainer"
       class="swiper-container mb-30px relative max-w-390px w-[calc(100vw-40px)]">
    <van-swipe class="swipe rounded-7px"
               :autoplay="interval"
               indicator-color="white">
      <van-swipe-item v-for="(item, index) in bannerArr"
                      :key="index"
                      @click="clickBanner(item)">
        <img :src="item.bannerLink"
             class="w-100%"
             :alt="item.solutionSet[0].name">
      </van-swipe-item>

      <template #indicator="{ active, total }">
        <!-- 避免Teleport的target未加载 -->
        <Teleport v-if="swiperContainer"
                  :to="swiperContainer">
          <div class="flexD absolute bottom--10px w-100%">
            <div v-for="(item, index) in total"
                 :key="index"
                 :class="{active: index === active}"
                 class="indicator mr-3px" />
          </div>
        </Teleport>
      </template>
    </van-swipe>

    <ClassfyPop v-model="showClassfyPop"
                :current="current"
                :info="bannerArr" />
  </div>
</template>

<script lang='ts' setup>
import ClassfyPop from './children/ClassifyPop.vue'
import api from '@/api'
import { Banner } from '@/api/modules/banner'
import bannerPoint from './point'

const swiperContainer = ref()
const showClassfyPop = ref(false)

const interval = ref(10000)
const bannerArr = ref<Banner[]>([])

const current = ref<number>()

onMounted(async () => {
  const res = await api.getBannerConfig()
  interval.value = res.interval * 1000
  bannerArr.value = res.banners
  console.log(res)
})

function clickBanner(info) {
  bannerPoint.h5Post('clickCustomerServiceIMBanner')

  current.value = info.solutionSet[0].id
  showClassfyPop.value = true
}
</script>

<style scoped lang='scss'>
.van-swipe-item {
  color: #fff;
  text-align: center;
  background-color: #39a9ed;
  width: 390px;
  max-height: 109px;
  display: flex;
}

.indicator {
  height: 5px;
  width: 5px;
  background: #BAC8D1;
  border-radius: 50%;
}

.active {
  width: 18px;
  height: 5px;
  background: #009EF8;
  border-radius: 2px;
}
</style>
