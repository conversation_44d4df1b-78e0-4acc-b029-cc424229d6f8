<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  :课程到期查询
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="min-h-100vh w-100vw bg-#F2F9FC pb-20px">

    <div v-if="courseList.length === 0"
         class="flexD w-100% flex-col h-100% pt-40%">
      <img src="@/assets/cry.png"
           class="w-268px h-222px mb-32px">
      <div class="c-#3FBAFF text-30px">暂未查询到名下有效课程信息</div>
    </div>

    <div v-if="courseList.length !== 0"
         class="flex flex-col justify-center items-center pt-30px">
      <div class="w-100%">
        <div class="c-#333 font-500 text-30px mb-17px pl-[calc((100vw-200px-180px-138px-190px)/2)]">您的课程剩余时长如下：</div>
      </div>

      <div v-for="(courseStage, idx) in courseStageList"
           :key="idx">
        <div v-if="courseStage.length !== 0"
             class="mb-30px">
          <div class="c-#3FBAFF text-30px font-500 my-12px">{{ courseStage[0].stage }}</div>

          <div class="flexD flex-col b b-solid b-#3FBAFF">
            <div class="flexD c-white w-100%">
              <div class="h-120px w-200px bg-#3FBAFF flexD text1 box-border">
                <span>课程名称</span>
              </div>
              <div class="h-120px w-180px bg-#7EDCF9 flexD text1">
                <span>剩余时长</span>
              </div>
              <div class="h-120px w-138px bg-#7EDCF9 flexD text1">
                <span>课程类型</span>
              </div>
              <div class="h-120px w-190px bg-#7EDCF9 flexD flex-1">
                <span>到期时间</span>
              </div>
            </div>

            <div v-for="(course, index) in courseStage"
                 :key="index"
                 class="flexD c-#333 text2 h-120px content-box">
              <div class="w-200px flexD h-100% text1 box-border px-10px">
                <span class="text-28px">{{ course.id }}</span>
              </div>
              <div class="w-180px text1 h-100% flexD">
                <span class="text-28px">{{ course.days }}天{{ course.hours }}小时</span>
              </div>
              <div class="w-138px text1 h-100% flexD">
                <span v-if="course.courseType === 'vip'"
                      class="c-#3FBAFF">同步课</span>
                <span v-if="course.courseType === 'special'"
                      class="c-#FE9647">专项课</span>
              </div>
              <div class="w-190px h-100% flexD text-center">
                <span class="text-28px">{{ course.expired }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang='ts' setup>
import { getCourse, Course } from '@/api'
import { getQueryObject } from '@guanghe-pub/onion-utils'
import point from '@/script/point'

const courseList = ref<Course[]>([])

const courseStageList: any = computed(() => {
  return courseList.value.reduce((pre, cur) => {
    return {
      ...pre,
      [cur.stage]: [...(pre[cur.stage] || []), cur]
    }
  }, {
    '小学': [],
    '初中': [],
    '高中': [],
    '中职': []
  })
})

onMounted(async () => {
  const { userId } = getQueryObject()

  point.post('getCourseExpirationInquiryPage')

  if (userId) {
    const res = await getCourse(userId)
    courseList.value = res.items
  }
})
</script>

<style scoped lang='scss'>
.text1 {
  border-right: 1px solid #3FBAFF;
}

.text2 {
  border-top: 1px solid #3FBAFF;
}

.content-box {
  > div {
    text-align: center;
  }
}
</style>
