import { defineStore } from 'pinia'
import { searchOrder, Order } from '@/api'

interface OrderState {
  orderList: Order[]
  loading: boolean
  finished: boolean
  pages: number
  pageSize: 10
  total: number

  userId: string
  phone: string

  unSearch: boolean // 没有搜索状态
}

export const useOrder = defineStore({
  id: 'Order',
  state(): OrderState {
    return {
      orderList: [],
      loading: false,
      finished: false,
      pageSize: 10,
      pages: 1,
      total: 0,
      userId: '',
      phone: '',
      unSearch: true
    }
  },
  getters: {
  },
  actions: {
    async getList() {
      if (this.unSearch) this.unSearch = false
      const par:any = {
        pages: this.pages,
        pageSize: this.pageSize,
      }

      if (this.userId) par.userId = this.userId
      if (this.phone) par.mobile = this.phone

      const res = await searchOrder(par)

      this.total = res.total

      return res.items
    },
    async onLoad() {
      this.loading = true
      const list = await this.getList()
      this.loading = false

      this.orderList = [...this.orderList, ...list]

      if (this.orderList.length >= this.total) {
        this.finished = true
        return
      }

      this.pages += 1
    },
    reset() {
      this.orderList.length = 0
      this.pages = 1
      this.total = 0
      this.finished = false
      this.loading = false
    }
  },
})
