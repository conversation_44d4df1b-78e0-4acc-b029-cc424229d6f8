<!--
 * @Date         : 2024-01-19 18:26:48
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="relative z-9 px-30px pt-20px">
    <div v-for="(teacher, index) in teachers"
         :key="index"
         class="card">
      <div class="pl-30px c-#6E829A py-35px flex flex-col justify-between items-start  w-690px h-257px">
        <div class="c-#1885FF">洋葱ID：{{ teacher.onionId }}</div>
        <div>当前学校：{{ teacher.schoolName }}</div>
        <div>当前行政班：{{ teacher.name }}</div>
        <div class="flexD">
          <span>业务受理电话：{{ teacher.phone }}</span>
          <el-button type="primary"
                     class="ml-20px"
                     round
                     size="small"
                     plain
                     @click="copy(teacher.phone)">复制号码</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { Teacher } from '@/api'
import { copyStr } from '#/script/index'
import { showToast } from 'vant'
import 'vant/es/toast/style'

console.log('thisss', copyStr)

const props = defineProps<{
  teachers: Teacher[]
}>()

function copy(phone) {
  // copyStr(phone)
  showToast('成功复制号码至剪切板中～')
}
</script>

<style scoped lang='scss'>
.card {
  background-image: url('@/assets/card.png');
  background-size: 100% 100%;
}
</style>
