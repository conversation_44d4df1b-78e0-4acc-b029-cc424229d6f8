<!--
 * @Date         : 2024-01-19 16:19:29
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="search-container">
    <van-search v-model="searchVal"
                clearable
                show-action
                type="digit"
                maxlength="11"
                placeholder="请输入洋葱ID">
      <template #action>
        <div class="flexD">
          <van-button round
                      class="w-134px h-58px!"
                      type="primary"
                      @click="search">搜索</van-button>
        </div>
      </template>
    </van-search>
  </div>
</template>

<script lang='ts' setup>
import { showFailToast } from 'vant'
import 'vant/es/toast/style'

const searchVal = ref('')

const emits = defineEmits(['search'])

function search() {
  if (searchVal.value.length > 11 || searchVal.value.length < 8) {
    showFailToast('请输入正确的洋葱ID')
    return
  }

  emits('search', {
    onionId: searchVal.value
  })
}

</script>

<style scoped lang='scss'>
.search-container {
  :deep(.van-search__content) {
    border-radius: 36px;
    background-color: white;
  }
  :deep(.van-search) {
    background-color: unset;
  }
  :deep(.van-search__field) {
    background-color: white;
    border-radius: 36px;
  }
  :deep(.van-icon-clear) {
    z-index: 9999;
  }
}
</style>
