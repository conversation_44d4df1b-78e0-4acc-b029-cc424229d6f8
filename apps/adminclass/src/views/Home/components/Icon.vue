<!--
 * @Date         : 2024-01-19 17:46:37
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="absolute top-305px w-100% flexD z-9">
    <div v-if="noData"
         class="flexD flex-col">
      <img class="w-368px 264px"
           src="@/assets/default_icon.png">
      <div class="c-#6E829A text-30px mt-20px">当前查询账号暂未加入行政班哦</div>
    </div>

    <div v-if="!noData"
         class="flexD flex-col">
      <img src="@/assets/nodata_icon.png"
           class="w-368px h-264px">
      <div class="c-#6E829A text-30px mt-20px">行政班查询</div>
    </div>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  noData: boolean
}>()
</script>

<style scoped lang='scss'>

</style>
