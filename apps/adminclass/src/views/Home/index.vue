<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  : home
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <common-container>
    <van-notice-bar v-if="!inNative"
                    text="洋葱ID查看位置：洋葱学园APP-我的-点击头像，即可查看~" />

    <Search v-if="showSearch"
            @search="getTeacher" />

    <Icon v-if="info.length === 0"
          :no-data="noClass" />

    <Content v-if="info.length !== 0"
             :teachers="info" />
  </common-container>
</template>

<script lang='ts' setup>
import api, { Teacher } from '@/api'
import { getQueryObject, isInNative } from '@guanghe-pub/onion-utils'
import point from '@/script/point'
import { showToast } from 'vant'

import Search from './components/Search.vue'
import Icon from './components/Icon.vue'
import Content from './components/Content.vue'

const info = ref<Teacher[]>([])
const noClass = ref(false)
const showSearch = ref(true)

async function getTeacher(par) {
  const res:any = await api.getTeacher(par)
  if (res.code === 205) {
    showToast(res.message)
    return
  }
  info.value = res.teachers
  if (res.teachers.length === 0) noClass.value = true
}

const inNative = ref(true)

onMounted(async () => {
  point.h5Post('getAdministrativeClassInquiryPageH5')
  const { userId } = getQueryObject()

  if (userId) {
    showSearch.value = false
    getTeacher({
      userId
    })
  }

  setTimeout(() => {
    inNative.value = isInNative()
    console.log('isInNative', inNative.value)
  }, 1000)

})
</script>

<style scoped lang='scss'>

</style>
