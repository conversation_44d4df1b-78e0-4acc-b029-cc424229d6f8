<!--
 * @Date         : 2024-01-30 15:43:16
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="p-10px">
    <div class="mb-10px">
      <el-text type="info">搜索结果{{ questionStore.questionList.value.length }}条</el-text>
    </div>
    <div>
      <el-card v-for="(item, index) in questionStore.questionList.value"
               :key="index"
               class="mb-10px">

        <template #header>
          <div class="flex justify-between">
            <el-text type="primary"
                     size="large">{{ item.baseQuestion }}</el-text>

            <el-button type="primary"
                       class="ml-5px"
                       :icon="DocumentCopy"
                       circle
                       @click="copyInnerText(item.answer)" />
          </div>
        </template>


        <Question :content="item.answer" />
      </el-card>
    </div>
  </div>
</template>

<script lang='ts' setup>
import Question from '../components/Question.vue'
import { useQuestion } from '../store'
import { DocumentCopy } from '@element-plus/icons-vue'
import copyInnerText from '../script/copyInnerText'

const questionStore = useQuestion()


</script>

<style scoped lang='scss'>

</style>
