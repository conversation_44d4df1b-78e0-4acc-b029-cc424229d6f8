<!--
 * @Date         : 2024-01-30 15:19:20
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="fixed top-0 w-100% bg-white z-999">
    <div class="flex justify-start p-10px">
      <el-input v-model="keyword"
                class="max-w-400px!"
                clearable
                placeholder="关键词"
                @keyup.enter="search">

        <template #prepend>
          <el-select v-model="mode"
                     class="w-130px!"
                     placeholder="Select">
            <el-option label="搜索问题"
                       value="question_and_similar" />
            <el-option label="搜索答案"
                       value="answer" />
            <el-option label="搜索全部"
                       value="all" />
          </el-select>
        </template>

      </el-input>

      <el-button type="primary"
                 class="ml-10px"
                 @click="search">搜索</el-button>
    </div>

    <div class="relative">
      <TalkSearchGuide ref="GuideRef"
                       @click-guide="searchGuideQuestion" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useQuestion } from '../store'
import api from '@/api'
import { useUrlSearchParams } from '@vueuse/core'
import TalkSearchGuide from '../components/TalkSearchGuide.vue'

const questionStore = useQuestion()
const GuideRef = ref()

// const { sign, email }: {sign: string; email: string} = useUrlSearchParams('history')
const { sign, email } = Object.fromEntries(decodeURIComponent(location.search.slice(1)).split('&').map(item => item.split('=')))
console.log('thisss', sign, email)

async function search() {
  if (keyword.value.trim() === '') {
    ElMessage.error('请输入内容后重试！')
  }

  const res = await api.search({
    searchMode: mode.value,
    content: keyword.value,
    email: email ?? '',
    sign: sign ?? ''
  })

  questionStore.questionList.value = res.list
  GuideRef.value.hiddeGuide()
}

const unWarch = ref(false)
function searchGuideQuestion(question: string) {
  unWarch.value = true
  keyword.value = question
  search()
  nextTick(() => {
    unWarch.value = false
  })
}

const keyword = ref('')
const mode = ref<'question' | 'answer' | 'similar' | 'all' | 'question_and_similar'>('question_and_similar')

watch(keyword, newV => {
  if (unWarch.value) return
  if (mode.value !== 'question_and_similar') return
  GuideRef.value.changeSearch({
    keyword: newV,
    email: email,
    sign: sign
  })
})

</script>

<style scoped lang='scss'>

</style>
