<!--
 * @Date         : 2024-01-30 16:28:34
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="relative"
       :style="{
         'max-height': hidden ? `${maxH}px` : 'none',
         'overflow': hidden ? 'hidden' : 'inherit'
       }">
    <div ref="contentRef"
         class="content"
         v-html="content" />
  </div>
  <div v-if="exceed"
       class="w-100% flexD bg-white mt-10px">
    <el-button v-if="hidden"
               type="primary"
               size="small"
               :icon="ArrowDownBold"
               circle
               @click="hidden = false" />
    <el-button v-if="!hidden"
               type="primary"
               size="small"
               :icon="ArrowUpBold"
               circle
               @click="hidden = true" />
  </div>
</template>

<script lang='ts' setup>
import { ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue'

const contentRef = ref()
const maxH = ref(200)
const hidden = ref(true)

const exceed = ref(false)

onMounted(() => {
  nextTick(() => {
    calcExceed()
  })
})
const props = defineProps<{
  content: string
}>()

function calcExceed() {
  nextTick(() => {
    exceed.value = contentRef.value.clientHeight > maxH.value || contentRef.value.querySelector('img')
  })
}

watch(() => props.content, newV => {
  calcExceed()
  hidden.value = true
})
</script>

<style scoped lang='scss'>
.content {
  :deep(img) {
    width: 100%;
  }
  word-wrap: break-word;
}
</style>
