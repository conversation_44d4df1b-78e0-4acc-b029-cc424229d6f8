<!--
 * @Date         : 2023-11-13 11:06:54
 * @Description  : 输入词条检索引导
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="guide-container rounded-4px absolute bg-white w-90% z-99 left-5%">
    <div v-if="guideList.length !== 0">
      <div v-for="(item, index) in guideList"
           :key="index"
           class="py-12px px-37px"
           :class="[index !== guideList.length - 1 ? 'b b-b b-b-#ECECEC b-b-solid' : '']">
        <span class="c-#999 text"
              @click="clickGuide(item.question)"
              v-html="searchHighLight(item.question)" />
      </div>
    </div>

    <div v-if="guideList.length === 0 && haveSearch"
         class="h-120px flexD flex-col">
      <div class="c-#999 mb-3px">暂无搜索内容</div>
      <div class="c-#4F9FF9">您可以换个词试试</div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'

const emits = defineEmits(['clickGuide'])

const guideList = ref<{
    question: string
    docId: string
  }[]>([])

async function getGuide(info) {
  const res = await api.searchGuide({
    keyword: info.keyword.trim(),
    email: info.email,
    sign: info.sign
  })

  if (info.keyword.length >= 2) {
    guideList.value = res.list
    keyword.value = info.keyword
  }
}

const haveSearch = ref(false)
const keyword = ref('')

function changeSearch(info) {
  const val = info.keyword
  if (val.length >= 2) {
    getGuide(info)
    haveSearch.value = true
  } else {
    guideList.value.length = 0
    haveSearch.value = false
  }
}

function searchHighLight(str:string) {  // 搜索高亮
  // 使用<span>标签标记指定字符
  const targetStr = keyword.value.replace(/(\++)/g, '\\$1').trim() // 处理+字符，避免+被识别为量词
  let markedString = str.replace(new RegExp(targetStr, 'gi'), function(match) {
    return '<span style="color: #4F9FF9;">' + match + '</span>'
  })

  return markedString
}

function clickGuide(question: string) {
  emits('clickGuide', question)
}

function hiddeGuide() {
  guideList.value.length = 0
  haveSearch.value = false
}

defineExpose({ changeSearch, hiddeGuide })
</script>

<style scoped lang='scss'>
.guide-container {
  box-shadow: 0px 7px 8px 0px #999;
}

.text {
  @include textHideLine1;
  width: 100%;
  // width: 560px;
  display: inline-block;
}
</style>
