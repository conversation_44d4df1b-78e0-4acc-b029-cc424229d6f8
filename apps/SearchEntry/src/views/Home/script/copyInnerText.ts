import { copyStr } from '@kefuweb/utils'

export default function(htmlStr: string) {
  // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
  let tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlStr

  // 移除所有a,img,video标签
  tempDiv.querySelectorAll('a, img, video').forEach(linkElement => {
    linkElement.remove()
  })

  copyStr(tempDiv.innerText)

  ElMessage.success('复制成功')
}

async function copy(htmlStr: string) {
  // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
  let tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlStr

  // 用于存储媒体元素和生成的唯一标识符
  const mediaMap = new Map()

  // 生成唯一标识符
  function generateId() {
    return `media_${Math.random().toString(36).substr(2, 9)}`
  }

  // 移除所有a标签
  tempDiv.querySelectorAll('a').forEach(linkElement => {
    linkElement.remove()
  })

  // 遍历并替换所有图片和视频标签
  tempDiv.querySelectorAll('img, video').forEach(media => {
    const id = generateId()
    mediaMap.set(id, media) // 存储媒体HTML
    const placeholder = document.createTextNode(`{${id}}`) // 创建占位符
    media?.parentNode?.replaceChild(placeholder, media) // 替换媒体为占位符
  })

  // 获取处理后的HTML字符串
  let processedHTML = tempDiv.innerText

  // 使用正则表达式进行分割 过滤掉空字符串
  let splitArr = processedHTML.split(/(\{media_[a-z0-9]+\})/).filter(item => item !== '')

  let clipboardArr:any = splitArr.map(async item => {
    if (/(\{media_[a-z0-9]+\})/.test(item)) {
      const ele =  mediaMap.get(item.slice(1, -1))
      const src = ele.src.replace('https://wuhan-file.tos-cn-beijing.volces.com/', 'https://wuhan-static.yangcong345.com/')
      const imageBlob = await fetch(src).then(res => res.blob())
      // return new ClipboardItem({ 'image/png': imageBlob })
      // return new ClipboardItem({ 'image/png': imageBlob })
      return new ClipboardItem({ 'video/mp4': imageBlob })
    } else {
      // return new ClipboardItem({ 'text/plain': item })
    }
  })

  const target = await Promise.allSettled(clipboardArr)
  const targetArr = target.map((item:any) => item.value).filter(item => item)
  console.log(targetArr)
  // 复制到剪切板
  navigator.clipboard.write(targetArr)
    .then(() => console.log('内容已复制到剪切板。'))
    .catch(err => console.error('复制失败:', err))

  // 输出处理后的HTML查看结果
  console.log(processedHTML)

  ElMessage.success('复制成功')
}
