import { createGlobalState } from '@vueuse/core'

export const useQuestion = createGlobalState(() => {
  const questionList = ref<any[]>([])
  const paramData = ref()

  function setQuestionList(list) {
    questionList.value = list
  }

  // onBeforeMount(() => {
  //   window.addEventListener('message', event => {
  //     let data = JSON.stringify(event.data)
  //     paramData.value = data
  //     console.log(
  //       `对接页面接收参数 postmessage 形式1： ${JSON.stringify(event.data)}`
  //     )
  //   })
  // })

  return { questionList, setQuestionList, paramData }
})
