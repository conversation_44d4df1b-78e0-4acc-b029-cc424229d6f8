import Axios from './axios'

export interface Question {
  baseQuestion: string
  answer: string
  docId: string
  qandaUniqueId: string
  qandaQuestions: {
    qandaUniqueId: string
    question: string
    qandaQuestionId: string
    updatedAt: string
    trainId: string
  }[]
}

export default {
  search(par: { email: string; sign: string; searchMode: 'question' | 'answer' | 'similar' | 'all' | 'question_and_similar'; content: string }): Promise<{
    list: Question[]
    total: string
  }> {
    return Axios.get('/web/qanda/sobotSearch', par)
  },
  searchGuide(par: {
    email: string
    sign: string
    keyword: string
  }): Promise<{
    list: {
      question: string
      docId: string
    }[]
  }> {
    return Axios.post('/web/qanda/sobotSearch/question', par)
  }
}
