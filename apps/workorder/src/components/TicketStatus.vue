<!--
 * @Date         : 2023-12-29 17:41:05
 * @Description  : 工单状态
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-132px h-44px rounded-tr-8px rounded-bl-24px flexD"
       :style="[`background-color: ${statusMap[status].bgColor}`]">
    <span class="text-26px"
          :style="[`color: ${statusMap[status].color}`]">{{ statusMap[status].label }}</span>
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  status: 'wait' | 'processing' | 'solved'
}>()

const statusMap = {
  wait: {
    label: '待受理',
    color: '#3179FF',
    bgColor: '#DBE8FF'
  },
  processing: {
    label: '受理中',
    color: '#EB4124',
    bgColor: '#FFDDD7'
  },
  solved: {
    label: '已解决',
    color: '#1B920C',
    bgColor: '#D4F5D3'
  }
}

defineExpose({ statusMap })
</script>

<style scoped lang='scss'>

</style>
