import { defineStore } from 'pinia'

interface workorderState {
  logistics: any[]
}

export const useWorkorder = defineStore({
  id: 'WORKORDER',
  state(): workorderState {
    return {
      logistics: []
    }
  },
  getters: {

  },
  actions: {
    getTicketOrder(ticketID) {
      return this.logistics.find(item => {
        return item.ticketID === ticketID
      })
    }
  },
  persist: {
    paths: ['logistics']
  }
})
