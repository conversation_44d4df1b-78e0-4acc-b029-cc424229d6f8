/*
 * @Date         : 2023-12-29 16:28:21
 * @Description  : 工单相关
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import Axios from '@/api/axios'

export interface Ticket {
  title: string
  details: string
  classification: string
  status: 'wait' | 'processing' | 'solved'
  acceptedBy: string
  ticketID: string
  createdAt: string
  updatedAt: string
  pushedAt: string
  updateLogistics: boolean
}
/**
 * @description: 用户工单列表
 * @url /web/ticket
 * @param {string} userId
 */
export function getTicket(userId: string): Promise<{
  list: Ticket[]
  total: number
}> {
  return Axios.get('/web/ticket', {
    userId
  })
}

/**
 * @description: 工单催一催
 * @url /web/ticket/push
 * @param {string} ticketID
 */
export function pushTicket(ticketID: string) {
  return Axios.post('/web/ticket/push', { ticketID })
}


/**
 * @description: 工单详情
 * @url /web/ticket/{ticketID}
 * @param {string} ticketID
 */
export function TicketDetail(ticketID: string): Promise<Ticket> {
  return Axios.get(`/web/ticket/${ticketID}`)
}

/**
 * @description: 上传物流信息
 */
export function uploadLogistics(par: {
  ticketID: string
  logistics: {
    name: string
    classification: string
    number: string
  }[]
}) {
  return Axios.post(`/web/ticket/logistics/${par.ticketID}`, par)
}
