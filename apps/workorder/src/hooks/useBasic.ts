/*
 * @Date         : 2023-08-14 14:54:49
 * @Description  : 基本功能hooks
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-08-31 15:17:07
 */

// 组件v-model定义
export function useModel(props, emit, modelName = 'modelValue') {
  const name = modelName

  const value = computed({
    get() {
      return props[name]
    },
    set(value) {
      emit(`update:${name}`, value)
    }
  })

  return value
}
