<!--
 * @Date         : 2024-12-04 17:53:33
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <div v-if="success"
         class="px-50px mt-40px">
      <div class="bg-white p-40px rounded-8px">
        <div class="flex items-center mb-20px">
          <van-icon name="passed"
                    color="#3179FF"
                    size="20px" />
          <span class="c-#3179FF font-600 text-26px ml-10px">已上传快递信息：</span>
        </div>

        <div v-for="(item, index) in info"
             :key="index"
             :class="{'mb-20px': index !== info.length - 1}"
             class="content">
          <div class="w-121px mr-18px"><span>{{ item.classification.join('、') }}</span></div>
          <div class="w-151px mr-18px"><span>{{ item.name }}</span></div>
          <div class="w-211px"><span>{{ item.number }}</span></div>
        </div>
      </div>
    </div>

    <div v-if="!success">
      <van-popup v-model:show="visible"
                 round>
        <div class="px-40px py-50px w-600px box-border">
          <div>
            <div class="flexD">
              <div class="font-600 text-32px c-#3179FF">信息确认</div>
            </div>
            <div class="mt-33px mb-23px font-500 text-24px c-#6084D2">请您二次核对上传的快递信息：</div>
          </div>

          <div v-for="(item, index) in info"
               :key="index"
               class="content mb-20px">
            <div class="w-121px mr-18px"><span>{{ item.classification.join('、') }}</span></div>
            <div class="w-151px mr-18px"><span>{{ item.name }}</span></div>
            <div class="w-211px"><span>{{ item.number }}</span></div>
          </div>

          <el-text type="danger"
                   class="font-bold"
                   size="small">一经确认无法修改</el-text>

          <div class="flexD mt-30px">
            <div class="w-221px h-81px bg-white b b-solid b-#3179FF rounded-40px flexD mr-29px"
                 @click="visible=false">
              <span class="c-#3179FF font-400 text-32px">取消</span>
            </div>
            <div class="w-221px h-81px bg-#3179FF b b-solid b-#3179FF rounded-40px flexD"
                 @click="confirm">
              <span class="c-white font-400 text-32px">确认</span>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import api from '@/api'
import { useRoute } from 'vue-router'
import { useWorkorder } from '@/store/workorder'

const route = useRoute()
const workorderStore = useWorkorder()

const visible = defineModel<boolean>('visible')
const success = defineModel<boolean>('success')

const props = defineProps<{
  info: any[]
}>()

async function confirm() {
  const ticketID: string = route.query.ticketID as string

  try {
    const param = {
      ticketID,
      logistics: props.info.map(item => {
        return {
          ...item,
          classification: item.classification.join('、')
        }
      })
    }
    workorderStore.logistics.push(param)
    await api.uploadLogistics(param)
    // workorderStore.logistics.push(param)

    visible.value = false
    success.value = true
  } catch (error) {
    console.log('error', error)
  }
}
</script>

<style scoped lang='scss'>
.content {
  display: flex;
  > div {
    box-sizing: border-box;
    height: 61px;
    border: 1px solid #6084D2;
    padding: 22px 15px;
    color: #6084D2;
    border-radius: 8px;
    font-size: 22px;
    font-weight: 300;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      @include textHideLine1;
    }
  }
}
</style>
