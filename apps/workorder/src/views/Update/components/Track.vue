<!--
 * @Date         : 2024-12-03 18:30:40
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flexD w-100vw mt-19px">
    <!-- <el-select v-model="goods"
               placeholder="选择商品类别"
               class="w-191px! mr-19px">
      <el-option v-for="item in goodsOptions"
                 :key="item"
                 :label="item"
                 :value="item" />
    </el-select> -->

    <div class="flexD mr-19px w-191px h-61px bg-white b b-solid b-#6084D2 rounded-8px box-border px-10px"
         @click="showPickerGoods = true">
      <span class="text-22px font-300 c-#6084D2 mr-10px line-height-30px textHideLine1">{{ data?.classification.join('、') || '选择商品类别' }}</span>
      <van-icon name="arrow-down"
                color="#6084D2"
                size="10px" />
    </div>

    <van-popup v-model:show="showPickerGoods"
               round
               position="bottom">
      <!-- <van-picker :columns="columnHandle(goodsOptions)"
                  @cancel="showPickerGoods = false"
                  @confirm="confirmGoods" /> -->

      <div class="p-30px">
        <div class="mb-30px">选择商品类别</div>

        <van-checkbox-group v-model="data!.classification "
                            shape="square">
          <van-checkbox v-for="(item, index) in goodsOptions"
                        :key="index"
                        class="mb-15px"
                        :name="item">{{ item }}</van-checkbox>
        </van-checkbox-group>
      </div>
    </van-popup>

    <div class="flexD mr-19px w-191px h-61px bg-white b b-solid b-#6084D2 rounded-8px box-border"
         @click="showPickerExpress = true">
      <span class="text-22px font-300 c-#6084D2 mr-10px line-height-30px textHideLine1">{{ data?.name || '选择快递公司' }}</span>
      <van-icon name="arrow-down"
                color="#6084D2"
                size="10px" />
    </div>

    <van-popup v-model:show="showPickerExpress"
               round
               position="bottom">
      <van-picker :columns="columnHandle(expressOptions)"
                  @cancel="showPickerExpress = false"
                  @confirm="confirmExpress" />
    </van-popup>

    <!-- <el-select v-model="express"
               placeholder="选择快递公司"
               class="w-191px! mr-19px">
      <el-option v-for="item in expressOptions"
                 :key="item"
                 :label="item"
                 :value="item" />
    </el-select> -->

    <div class="order-input">
      <el-input v-model="data!.number"
                maxlength="50"
                placeholder="请输入退换快递单号"
                class="w-251px! h-61px!" />
    </div>

    <div v-if="orderList.length > 1"
         class="ml-10px">
      <van-icon name="delete-o"
                color="#F56C6C"
                size="20px"
                @click="orderList.splice(idx, 1)" />

    </div>
  </div>
</template>

<script lang='ts' setup>
import goodsOptions from '../const/goods'
import expressOptions from '../const/express'
import { defineModel } from 'vue'

const data = defineModel<{
  name: string
  classification: any[]
  number: string
}>()
const orderList = defineModel<any>('orderList')
const props = defineProps<{
  idx: any
}>()

function columnHandle(options) {
  return options.map(item => {
    return {
      text: item,
      value: item
    }
  })
}

const showPickerGoods = ref(false)
const goods = ref('')
function confirmGoods({ selectedOptions }) {
  showPickerGoods.value = false
  data.value!.classification = selectedOptions[0].text
}

const showPickerExpress = ref(false)
const express = ref('')
function confirmExpress({ selectedOptions }) {
  showPickerExpress.value = false
  data.value!.name = selectedOptions[0].text
}

const order = ref('')
</script>

<style scoped lang='scss'>
.order-input {
  :deep(.el-input__wrapper) {
    box-shadow: none;
    border: 1px solid #6084D2 !important;
  }
  :deep(input) {
    color: #6084D2;
    font-weight: 300;
    font-size: 22px;
    &::placeholder {
      color: #6084D2;
      font-weight: 300;
      font-size: 22px;
    }
  }
}
</style>
