<!--
 * @Date         : 2024-12-03 17:46:05
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <common-container>
    <div class="relative z-9">
      <div class="c-#6E829A text-26px px-51px font-300 pt-39px">
        <div class="mb-10px">温馨提示：</div>
        <div>
          您好，您的订单实物寄回可能存在多个收货地址（即多个包裹），请您区分每个包裹包含了哪些物品，若仅一个包裹，填写一次即可；
          <el-text class="font-bold"
                   type="danger">若多个包裹，则填写多次后统一提交确认。</el-text>
          请您确保信息填写正确，一经确认无法修改~
        </div>
      </div>
      <div v-if="historyLogistics?.length !== 0"
           class="mt-19px">
        <div v-for="(item, index) in historyLogistics"
             :key="index"
             class="flexD">
          <el-input disabled
                    class="w-191px! h-61px text-22px! font-300! mr-19px textHideLine1"
                    :model-value="item.classification" />
          <el-input disabled
                    class="w-191px! h-61px text-22px! font-300! mr-19px"
                    :model-value="item.name" />
          <el-input disabled
                    class="w-251px! h-61px text-22px! font-300!"
                    :model-value="item.number" />
        </div>
      </div>

      <div v-if="!success && historyLogistics?.length === 0">
        <div v-if="orderList.length === 0"
             class="w-406px h-91px c-#6084D2 rounded-45px bg-white b b-#6084D2 b-dashed flexD absolute z-999 left-173px top-494px"
             @click="addOrder">
          <van-icon name="add-o"
                    class="text-44px!" />
          <div class="text-29px ml-16px">点击添加包裹</div>
        </div>

        <div v-if="orderList.length !== 0">
          <div v-for="(item, index) in orderList"
               :key="index">
            <Track v-model="orderList[index]"
                   v-model:orderList="orderList"
                   :idx="index" />
          </div>

          <div class="w-141px h-51px bg-#6084D2 flexD ml-40px rounded-8px mt-19px"
               @click="addOrder">
            <van-icon name="plus"
                      size="16px"
                      color="white" />
            <span class="c-white text-22px font-300 line-height-30px">添加包裹</span>
          </div>

          <div class="flexD mt-81px">
            <div class="w-301px h-81px bg-white b b-solid b-#3179FF rounded-40px flexD mr-31px">
              <span class="c-#3179FF font-400 text-32px"
                    @click="router.back()">取消</span>
            </div>
            <div class="w-301px h-81px bg-#3179FF b b-solid b-#3179FF rounded-40px flexD"
                 @click="update">
              <span class="c-white font-400 text-32px">上传</span>
            </div>
          </div>
        </div>
      </div>


      <ConfirmPop v-model:visible="ConfirmPopVisible"
                  v-model:success="success"
                  :info="orderList" />
    </div>

  </common-container>
</template>

<script lang='ts' setup>
import Track from './components/Track.vue'
import ConfirmPop from './components/ConfirmPop.vue'
import { showToast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import 'vant/es/toast/style'
import { useWorkorder } from '@/store/workorder'

const workorderStore = useWorkorder()
const router = useRouter()
const route = useRoute()
const ConfirmPopVisible = ref(false)

const success = ref(false)

const orderTmp = {
  name: '',
  classification: [],
  number: ''
}
const orderList = ref<typeof orderTmp[]>([])

function addOrder() {
  orderList.value.push({ ...orderTmp })
}

function update() {
  const hasEmpty = orderList.value.some(item => {
    return item.name === '' || item.classification.length === 0 || item.number === ''
  })

  if (hasEmpty) {
    showToast('请填写完整信息')
    return
  }
  ConfirmPopVisible.value = true
}

const historyLogistics = ref<any[]>([])
onMounted(() => {
  const ticketID: string = route.query.ticketID as string

  const logistics = workorderStore.getTicketOrder(ticketID)
  console.log('innn', logistics.logistics)
  if (logistics) {
    historyLogistics.value = logistics.logistics
  }
})
</script>

<style scoped lang='scss'>

</style>
