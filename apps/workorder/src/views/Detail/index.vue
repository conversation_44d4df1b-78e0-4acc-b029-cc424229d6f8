<!--
 * @Date         : 2023-12-29 17:16:23
 * @Description  : 详情
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <common-container class="py-40px flex justify-center">
    <div class="w-690px rounded-8px bg-white relative z-9 h-fit p-30px box-border">
      <div class="c-#333 text-30px font-400 mb-30px">{{ }}</div>

      <div class="text-26px c-#6E829A content-box">
        <!-- <div>
          <span>工单内容：</span>
          <span class="detail break-words"
                v-html="info?.details" />
        </div> -->
        <div>
          <span>工单分类：</span>
          <span>{{ info?.classification }}</span>
        </div>
        <div>
          <span>处理状态：</span>
          <span>{{ TicketStatusRef?.statusMap[info?.status ?? 'wait'].label }}</span>
        </div>
        <div>
          <span>受理客服：</span>
          <span>洋葱客服组</span>
        </div>
        <div>
          <span>受理时间：</span>
          <span>{{ info?.createdAt }}</span>
        </div>
      </div>

      <div class="absolute right-10px top-10px">
        <TicketStatus ref="TicketStatusRef"
                      :status="info?.status ?? 'wait'" />
      </div>

      <div class="w-100% flexD">
        <van-button round
                    :disabled="isIn24Hour || info?.status === 'solved'"
                    class="h-56px! w-280px!"
                    type="primary"
                    @click="pushTicket">催一催 >></van-button>
      </div>
    </div>
  </common-container>
</template>

<script lang='ts' setup>
import api from '@/api'
import { useRoute } from 'vue-router'
import { Ticket } from '@/api/modules/ticket'
import TicketStatus from '@/components/TicketStatus.vue'
import dayjs from 'dayjs'
import { showToast } from 'vant'
import 'vant/es/toast/style'
import point from '@/script/point'


const route = useRoute()

const TicketStatusRef = ref()
const info = ref<Ticket>()
async function getDetail() {
  const ticketID = route.query.ticketID as string

  const res = await api.TicketDetail(ticketID)
  info.value = res
}

onMounted(() => {
  point.post('getCustomerServiceWorkOrderDetails')
  getDetail()
})

const isIn24Hour = computed(() => {
  return !dayjs(info.value?.createdAt).add(1, 'day').isBefore(dayjs())
})

function pushTicket() {
  point.post('clickCustomerServiceWorkOrderDetailsPageButton', {
    button: '催一催'
  })

  if (info.value?.pushedAt === '0001-01-01 00:00:00') {
    pushHandle()
  } else if (dayjs(info.value?.pushedAt).add(1, 'day').isBefore(dayjs())) {
    pushHandle()
  } else {
    showToast('您的工单在24小时内已催促，请您耐心等待，感谢理解～')
  }
}

async function pushHandle() {
  const ticketID = route.query.ticketID as string
  await api.pushTicket(ticketID)
  showToast('您的工单已收到，我们会尽快跟进并解决您的问题～')
  getDetail()
}
</script>

<style scoped lang='scss'>
.content-box {
  >div {
    margin-bottom: 27px;
  }
}

.detail {
  :deep(img) {
    width: -webkit-fill-available;
  }
}
</style>
