<!--
 * @Date         : 2023-12-29 16:26:42
 * @Description  : 工单列表
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <Empty v-if="isEmpty" />

  <div v-if="!isEmpty"
       class="list-container flex items-center justify-start flex-col pt-40px relative z-9">
    <div v-for="(ticket, index) in ticketList"
         :key="index"
         class="w-690px h-200px bg-white card relative pt-25px pl-20px box-border mb-24px">
      <div class="c-#333 text-30px mb-24px font-500">{{ formatTitle(ticket.classification) }}</div>
      <div class="text-28px mb-19px c-#6E829A">受理客服：洋葱客服组</div>
      <div class="text-28px c-#6E829A">{{ ticket.createdAt }}</div>

      <div class="absolute right-0px top-0px">
        <TicketStatus :status="ticket.status" />
      </div>

      <div v-if="checkUpload(ticket.classification) && !ticket.updateLogistics && !workorderStore.getTicketOrder(ticket.ticketID)"
           class="absolute right-10px bottom-88px"
           @click="goUpdate(ticket.ticketID)">
        <div class="h-43px w-132px rounded-21px bg-#3179FF b b-solid b-#3179FF flexD">
          <span class="text-22px c-white">添加包裹</span>
        </div>
      </div>

      <div v-if="workorderStore.getTicketOrder(ticket.ticketID)"
           class="absolute right-10px bottom-88px"
           @click="goUpdate(ticket.ticketID)">
        <div class="h-43px w-132px rounded-21px bg-#3179FF b b-solid b-#3179FF flexD">
          <span class="text-22px c-white">上传记录</span>
        </div>
      </div>

      <div class="absolute right-10px bottom-33px"
           @click="goDetail(ticket.ticketID)">
        <div class="h-43px w-132px rounded-21px bg-#EEF7FE b b-solid b-#6084D2 flexD">
          <span class="text-22px c-#6084D2">查看 </span>
          <van-icon name="arrow"
                    size="13px"
                    color="#6084D2" />
        </div>
      </div>
    </div>

    <div class="text-#6E829A fs-26 text-center w-100% mt-20px mb-40px">仅展示三个月内的工单信息</div>
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'
import { Ticket } from '@/api/modules/ticket'
import { getQueryObject } from '@guanghe-pub/onion-utils'
import { useRouter } from 'vue-router'
import TicketStatus from '@/components/TicketStatus.vue'
import Empty from '../children/Empty.vue'
import { useWorkorder } from '@/store/workorder'

const workorderStore = useWorkorder()

const ticketList = ref<Ticket[]>([])
async function getList() {
  const { userId } = getQueryObject()
  if (!userId) return

  const res = await api.getTicket(userId)
  ticketList.value = res.list

  if (ticketList.value.length === 0) isEmpty.value = true
}

onMounted(() => {
  getList()
  checkIsNative()
})

const router = useRouter()
function goDetail(ticketID: string) {
  router.push({
    path: '/Detail',
    query: { ticketID }
  })
}
function goUpdate(ticketID: string) {
  router.push({
    path: '/Update',
    query: { ticketID }
  })
}


const isEmpty = ref(false)

function checkIsNative() {
  const { userId } = getQueryObject()

  if (!userId) {
    isEmpty.value = true
  } else {
    isEmpty.value = false
  }
}

function formatTitle(title) {
  if (title.length <= 12) return title
  else return `${title.slice(0, 12)}...`
}

function checkUpload(classification: string) {
  const hasAuth = ['客服内部/退款业务',
    '客服内部/实物相关异常',
    '特殊申请及投诉/退款规则',
    '特殊申请及投诉/物流相关投诉']
  return hasAuth.some(item => {
    return classification.includes(item)
  })
}
</script>

<style scoped lang='scss'>
.card {
  background-image: url('@/assets/card.png');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
