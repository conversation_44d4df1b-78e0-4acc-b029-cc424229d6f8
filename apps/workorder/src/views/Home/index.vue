<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  : home
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <common-container>
    <List />

    <div class="text-#6E829A fs-26 text-center absolute bottom-60px w-100%">仅展示三个月内的工单信息</div>
  </common-container>
</template>

<script lang='ts' setup>
import List from './children/List.vue'
import { getQueryObject, isInNative } from '@guanghe-pub/onion-utils'
import point from '@/script/point'

onMounted(() => {
  setTimeout(() => {
    console.log('inNative', isInNative())
    if (isInNative()) {
      point.post('getCustomerServiceWorkOrderHome')
    } else {
      point.h5Post('getCustomerServiceWorkOrderHome')
    }
  }, 1000)
})

</script>

<style scoped lang='scss'>
</style>
