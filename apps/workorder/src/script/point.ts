import { BuryPoint, getQueryObject } from '@guanghe-pub/onion-utils'
import axios from 'axios'
/**
 * https://guanghe.feishu.cn/sheets/shtcnTP5rhn8AJIpuQwCLJPACIc?sheet=b7c3e3
 */
const points = {
  getCustomerServiceWorkOrderHome: {
    category: 'activity',
    desc: '洋葱学院工单查询首页曝光',
  },
  getCustomerServiceWorkOrderDetails: {
    category: 'activity',
    desc: '洋葱学院工单详情页曝光',
  },
  clickCustomerServiceWorkOrderDetailsPageButton: {
    category: 'activity',
    data: ['button'],
    desc: '洋葱学院工单详情页按钮点击',
  }
}

export default new BuryPoint(points, {
  env: import.meta.env.VITE_GETHOST,
  axios
})
