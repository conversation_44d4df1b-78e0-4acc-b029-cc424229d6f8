<!--
 * @Date         : 2021-04-19 18:00:02
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="!userStore.fromPageNameError"
       class="flexD h-100vh w-100vw bg-#f0f2f5">
    <div id="IM"
         class="w-650px shadow-[var(--el-box-shadow-light)] relative">
      <div class="h-50px flex items-center bg-white">
        <div class="ml-20px flex items-center">
          <el-avatar class="mr-10px"
                     :size="26"
                     shape="circle"
                     :src="baseStore.avatar" />
          <span class="c-#2CABFB">{{ baseStore.nickName }}</span>
        </div>
      </div>

      <div class="content-container h-670px relative">
        <div id="msgBox"
             ref="msgBox"
             class="flex flex-col overflow-auto h-500px">
          <div v-if="baseStore.loadingCompleted"
               class="w-100% flexD mt-5px">
            <el-link v-if="!historyLoading && !noHistory"
                     class="c-white! text-12px"
                     :underline="false"
                     @click="getHistory">加载历史消息</el-link>
            <el-text v-if="historyLoading && !noHistory">加载中...</el-text>
          </div>


          <MsgBox :send-tmp-msg="sendTmpMsg" />
        </div>


        <div class="bottom-0 absolute w-100% h-170px flex justify-center items-end">
          <div v-if="baseStore.networkTimeout"
               class="h-80% flexD w-100%">
            <el-button type="info"
                       class="reconnect-btn"
                       @click="baseStore.webReload()">
              <el-icon color="#2CABFB"
                       size="22px"><Refresh /></el-icon>
              <span>刷新页面</span>
            </el-button>
          </div>

          <div v-if="(baseStore.timeout || baseStore.renew) && !baseStore.networkTimeout"
               class="h-80% flex items-end justify-center w-100%">
            <el-button type="info"
                       :loading="baseStore.connectLoading"
                       class="reconnect-btn"
                       @click="reconnect">
              <el-icon color="#2CABFB"
                       size="22px"><ChatDotSquare /></el-icon>
              <span>{{ baseStore.connectLoading ? '重连中': '继续咨询' }}</span>
            </el-button>
          </div>

          <div v-if="!baseStore.timeout && !baseStore.renew && !baseStore.networkTimeout"
               class="w-100% relative">
            <MenuBar />


            <div class="footer h-140px flexD">
              <div class="py-20px w-610px relative">
                <TalkSearchGuide v-model:text="textarea"
                                 type="pc"
                                 :send-msg="sendTmpMsg" />

                <el-input ref="msgTextareaRef"
                          v-model="textarea"
                          maxlength="100"
                          class="msg-input h-100% b-solid b-1px rounded-8px overflow-auto b-#2CABFB"
                          type="textarea"
                          :rows="5"
                          show-word-limit
                          resize="none"
                          placeholder="请简短描述您的问题"
                          @keydown="msgEvent" />

                <el-button class="absolute right-7px bottom-27px c-white!"
                           color="#2CABFB"
                           :disabled="!baseStore.initCompleted"
                           @click="sendAiMsg">发送</el-button>
              </div>

            </div>

          </div>

        </div>
      </div>

    </div>

    <ErrorDialog />
    <AdvisoryDialog />
  </div>

  <FromPageNameError v-if="userStore.fromPageNameError" />
</template>

<script lang='ts' setup>
import { ChatDotSquare, Refresh } from '@element-plus/icons-vue'
import { useBase } from '@/store/base'
import { useUser } from '@/store/user'

import useMqtt from '@/hooks/useMqtt'
import useGetMsg from '@/hooks/useGetMsg'
import useSendMsg from '@/hooks/useSendMsg'
import useHistory from '@/hooks/useHistory'
import useInit from '@/hooks/useInit'

import MsgBox from '@/components/MsgBox/index.vue'
import MenuBar from '@/components/MenuBar/index.vue'
import ErrorDialog from '@/components/ErrorDialog/pc.vue'
import TalkSearchGuide from '@/components/TalkSearchGuide/index.vue'
import AdvisoryDialog from '@/components/AdvisoryDialog/pc.vue'

import FromPageNameError from '@/components/FromPageNameError/index.vue'

const baseStore = useBase()
const userStore = useUser()

const msgTextareaRef = ref()
const msgBox = ref()
const textarea = ref('')

// 滚动到最下方
function scrollBottom() {
  nextTick(() => {
    if (msgBox.value) msgBox.value.scrollTop = msgBox.value?.scrollHeight
  })
}

const { sendTmpMsg, sendAiMsg, msgEvent } = useSendMsg({ textarea, scrollBottom })
const { getHistory, historyLoading, noHistory } = useHistory()
const { getMsg } = useGetMsg({ scrollBottom })

const { setHeart, getConnect, setMqtt, reconnect } = useMqtt(getMsg)

useInit({ setMqtt, setHeart, getConnect, scrollBottom })

</script>

<style scoped lang='scss'>
.content-container {
  // background: linear-gradient(0deg, #F0F1F1 57%, #A3E0FF 100%);
  background: url('https://fp.yangcong345.com/middle/1.0.0/assets/bg-38cc55e4040c1a81541e29c0059d26cf__w.png') no-repeat;
  background-size: cover;
}

.msg-input {
  background: white;
  :deep(.el-textarea__inner) {
    box-shadow: none;
  }
}
.footer {
  :deep(.el-textarea .el-input__count) {
    right: 80px;
    bottom: 15px;
  }
}

.reconnect-btn {
  background: white;
  border: 0;
  width: 90%;
  font-size: 15px;
  color: #666;
  padding: 32px 0;
  margin-bottom: 20px;
}
</style>
