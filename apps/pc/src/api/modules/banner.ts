
import Axios from '@/api/axios'

export interface QandaSet {
  solutionSetId: number
  score: number
  qandaUniqueId: string
  baseQuestion: string
  docId: string
}

export interface SolutionSet {
  id: number
  createdAt: string
  updatedAt: string
  deletedAt: string
  solutionId: number
  name: string
  score: number
  qandaSet: QandaSet[]
}

export interface Banner {
  bannerLink: string
  solutionSet: SolutionSet[]
}

export interface BannerConfig {
  banners: Banner[]
  interval: number
}


/**
 * @description: 获取移动端banner配置
 */
export function getBannerConfig(): Promise<BannerConfig> {
  return Axios.get('/web/bannerConfig')
}


export interface QuestionDetail {
  baseQuestion: string
  relatedQandas: {
    docId: string
    qandaBaseQuestion: string
    qandaUniqueId: string
  }[]
  answer: string
  docId: string
  qandaQuestions: {
    qandaQuestionId: string
    qandaUniqueId: string
    question: string
    trainId: string
    updatedAt: string
  }[]
  qandaUniqueId: string
  relatedGuideSentence: string
}
/**
 * @description: 获取词条对应的信息
 * @param {string} docId
 */
export function getQuestionDetail(docId: string): Promise<QuestionDetail> {
  return Axios.get(`/web/qanda/search/${docId}`)
}
