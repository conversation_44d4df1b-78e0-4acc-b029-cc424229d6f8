import Axios from '@/api/axios'

export interface OfflineMsg {
  type: number
  partnerId: string
  msgId: string
  content: string
  msgType: string
  aname: string
  aface: string
  params: string
  readAt: string
  createdAt: string
  read: string
  id: string
}


/**
 * @description: 获取离线信息
 * @url /web/message/offline
 */
export function getOffline(par: {
  userId: string
  sessionId: string
  startTime: number
  endTime: number
}):Promise<{
  messages: OfflineMsg[]
}> {
  return Axios.post('/web/message/offline', par)
}
