import Axios from '@/api/axios'
import { AxiosClass } from '@guanghe-pub/nexus-axios'

export interface TransferLaborRes {
  changeAgent: 'on' | 'off'
  delayChangeAgent: 'on' | 'off'
  delay: string
  messagePoint: string[]
}
export interface CheckChannelRes extends TransferLaborRes {
  access: boolean
  channelId: string
  channelName: string
  channelUniqueId: string
  solution: any
  clickEnable: string // 传送门为空则使用原始默认配置，为true/false则使用后台配置的配置
  score: number // 延迟问题匹配度
}
/**
 * @description 判断当前渠道是否开放
 * @url /web/config/:channel
 * @param {string} channel
 * @return {CheckChannelRes}
 */
export function checkChannel(channel: string, par: {
  userId: string
}): Promise<CheckChannelRes> {
  return Axios.get(`/web/config/${channel}`, par)
}


/**
 * @description: 发送埋点信息
 */
export function sendPoint(params) {
  const axios = new AxiosClass({
    reductAll: true
  })
  return axios.request({
    method: 'post',
    url: '',
    data: params,
    baseURL: import.meta.env.VITE_POINT,
  })
}

/**
 * @description: 判断会话状态 session是否过期
 * @url /web/session/{sessionUniqueId}
 */
export function checkSession(sessionUniqueId: string): Promise<{
  exits: boolean
}> {
  return Axios.get(`/web/session/${sessionUniqueId}`)
}

export interface Menu {
  menuId: string
  menus: Menu[]
  name: string
  show?: boolean
  menuDescription: {
    content: string
    icon: string
    menuId: string
    menuType: string
  }
}

/**
 * @description: 获取菜单栏列表
 * @url /web/session/{channelUniqueId}
 */
export function getMenuApi(channelUniquedId: string, par: {
  userId: string
}): Promise<{
  id: string
  menus: Menu[]
  name: string
  solutionType: string
}> {
  return Axios.get(`/web/menuSolution/${channelUniquedId}`, par)
}

/**
 * @description: 用户分层展示
 */
export function userLayered(par: {
  onionUserId: string
}): Promise<{
  show: boolean
}> {
  return Axios.post('/web/userShow', par)
}

/**
 * @description: 判断uuid是否正确
 * @param {string} uuid
 */
export function checkFromPageName(uuid: string): Promise<{
  pass: boolean
}> {
  return Axios.post(`/web/frompage/check`, {
    uuid
  })
}

/**
 * @description: 获取SN码
 */
export function getSN(onionId: string): Promise<{
  onionId: string
  snIds: string[]
}> {
  return Axios.get(`/web/sn/${onionId}`)
}

/**
 * @description: 获取用户信息
 * @param {string} onionUserId
 */
export function getUserInfo(onionUserId: string):Promise<{
  onionId: string
  nickname: string
  attribution: string
  role: string
  os: string
}> {
  return Axios.post('/web/userInfo', { onionUserId })
}
