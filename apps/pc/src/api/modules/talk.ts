import Axios from '@/api/axios'

export interface SearchGuideItem {
  question: string
  qandaUniqueId: string
}
/**
 * @description: 检索知识库引导
 * @url /web/qanda/search
 */
export function searchGuide(keyword: string): Promise<{
  list: SearchGuideItem[]
}> {
  return Axios.post('/web/qanda/search', { keyword })
}


/**
 * @description: 检索词条点击记录
 */
export function countGuide(par: {
  question: string
  clickType: 'guide' | 'search'
  qandaUniqueId: string
}) {
  return Axios.post('/web/qanda/count', par)
}

/**
 * @description: 点赞踩提交
 */
export function replyComment(par: {
  instance: {
    id?: string
    comment?: string
    label?: string
    commentType?: string
  }
}) {
  return Axios.put('/web/replyComment', par)
}
