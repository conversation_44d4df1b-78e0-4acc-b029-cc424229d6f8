import Axios from '@/api/axios'
import { SendMsgReq } from '@/types/mssage'
import { Guide } from '@/types/system/user'

export function heartbeat(par: {
  deviceId: string
  type: string
}) {
  return Axios.get('/web/ping', par)
}

export function getToken(par: {
  userType: 'customer' | 'kefu'
}): Promise<{
  accessKey: string
  deviceId: string
  instanceId: string
  token: string
  uid: string
  groupId: string
  topic: string
}> {
  return Axios.post('/web/token/check', par)
}

export function sendMsg(par: SendMsgReq): Promise<{sessionId: string}> {
  return Axios.post('/web/message/send', par)
}

/**
 * @description: 获取历史消息
 */
export function historyMsg(par: {
  uid: string
  pages: number
  pageSize: number
  channelId: string
  endTime: number
}) {
  return Axios.post('/web/message/pull', par)
}

/**
 * @description: 转人工
 * @url /web/changeAgent
 * @return access 是否可以转换客服 是否在时间内
 * @return noServiceSentence 服务器当前时间
 */
export function changeAgent(): Promise<{
  access: boolean // 是否可以转换客服 是否在时间内
  serverTime: string // 服务器当前时间
}> {
  return Axios.get('/web/changeAgent')
}

/**
 * @description: 连接mqtt 判断sessionid是否过期 获取guide基本信息
 * @url /web/session/connect
 */
export function connect(par: {
  sessionId?: string
  channelId: number
  sender: string
  senderType: string
  senderUid: string
  onionUserId?: string
}): Promise<{
  sessionId: string
  guide: Guide
  callReuse: boolean  // 判断sassionId是否在三分钟内进行了转人工
}> {
  return Axios.post('/web/session/connect', par)
}

