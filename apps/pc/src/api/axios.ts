/*
 * @Date         : 2024-05-14 16:38:27
 * @Description  : axios 配置
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { AxiosClass } from '@guanghe-pub/nexus-axios'
import axiosRetry from 'axios-retry'
import { useUser } from '@/store/user'
import { useError } from '@/store/error'
import { useBase } from '@/store/base'
import { useMessage } from '@/store/message'


const Axios = new AxiosClass({
  reductData: false,
  baseURL: import.meta.env.VITE_HOST_API
})

// axiosRetry(Axios.axios, {
//   retries: 3,
//   retryDelay: () => 0.5 * 1000, // 重复请求延迟（毫秒）
//   shouldResetTimeout: true, //  重置超时时间
// })

Axios.axios.interceptors.request.use(
  (config) => {
    const userStore = useUser()
    if (userStore.token) config.headers['Message-Token'] = userStore.token
    return config
  }
)

Axios.axios.interceptors.response.use(
  (res) => {
    return res
  },
  (error) => {
    console.log('inn', error)
    useError().basePortError(error)

    // 统一错误弹窗
    useError().showErrorDialog = true

    if (error.status === 401) {
      if (error.response.data.message === 'session is invalid') {
        sessionInvalid()
      }
    }
    if (error.status === 401) status401()
    if (error.status === 403) status403(error)

    return Promise.reject(error.response)
  }
)



export default Axios


function sessionInvalid() {
  useError().reconnectionNetwork()
  throw Error('sessionInvalid')
}

function status401() {
  useError().reconnectionMqtt()
  throw Error('tokenInvalid')
}

function status403(error) {
  if (error.response.data.reason === 'LIMIT_CONTROL') {
    ElMessage.error('当前咨询速度过快～请稍后继续咨询')
    useBase().loading = false
    useMessage().list.pop()
  } else {
    useError().showErrorDialog = true
  }

  throw Error('403')
}
