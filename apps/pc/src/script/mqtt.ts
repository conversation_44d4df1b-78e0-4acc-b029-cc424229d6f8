import dayjs from 'dayjs'
import Paho from 'paho-mqtt'
import { useBase } from '@/store/base'
import { useUser } from '@/store/user'
import api from '@/api'

export default class MQTT {
  topic: string
  instanceId: string // 实例 ID，购买后从控制台获取
  host: string // 设置当前用户的接入点域名，接入点获取方法请参考接入准备章节文档，先在控制台创建实例
  port: number // WebSocket 协议服务端口，如果是走 HTTPS，设置443端口
  useTLS: boolean // 是否走加密 HTTPS，如果走 HTTPS，设置为 true
  accessKey: string // 账号 accesskey，从账号系统控制台获取
  // secretKey: string // 账号的的 SecretKey，在阿里云控制台查看
  cleansession: boolean
  groupId: string // MQTT GroupID,创建实例后从 MQTT 控制台创建
  clientId: string // GroupId@@@DeviceId，由控制台创建的 Group ID 和自己指定的 Device ID 组合构成
  deviceId: string
  token: string

  mqtt: any
  reconnectTimeout: number
  username: string
  password: string

  cb: any
  lostMqttCb: any

  connectInterval: any
  constructor({
    accessKey, instanceId, deviceId, token
  }, cb, lostMqttCb) {
    this.cb = cb
    this.lostMqttCb = lostMqttCb

    this.accessKey = accessKey
    this.deviceId = deviceId
    this.token = token
    // this.secretKey = ''

    this.groupId = import.meta.env.VITE_GROUPID
    this.topic = import.meta.env.VITE_TOPIC

    this.instanceId = instanceId
    this.host = `${instanceId}.mqtt.aliyuncs.com`
    this.port = 443
    this.useTLS = true
    this.cleansession = true
    this.clientId = `${this.groupId}@@@${this.deviceId}`

    this.reconnectTimeout = 2000
    this.username = `Token|${this.accessKey}|${this.instanceId}`
    this.password = `R|${token}`

    this.mqtt = null

    this.connectInterval = null
  }

  MQTTreloadToekn(token: string, deviceId: string) {
    this.token = token
    this.password = `R|${token}`
    this.deviceId = deviceId
    this.clientId = `${this.groupId}@@@${this.deviceId}`
  }

  MQTTconnect() {
    const { host, port, clientId, username, onConnect, useTLS, password, MQTTconnect, reconnectTimeout, cleansession, onConnectionLost, onMessageArrived } = this
    this.mqtt = new Paho.Client(
      host, // MQTT 域名
      port, // WebSocket 端口，如果使用 HTTPS 加密则配置为443,否则配置80
      clientId// 客户端 ClientId
    )

    return new Promise((resolve, reject) => {
      let options:any = {
        onSuccess: () => {
          onConnect.call(this)
          resolve(true)
        },
        mqttVersion: 4,
        cleanSession: cleansession,
        keepAliveInterval: 5,
        onFailure: function (message) {
          setTimeout(MQTTconnect, reconnectTimeout)
        }
      }
      this.mqtt.onConnectionLost = onConnectionLost.bind(this)
      this.mqtt.onMessageArrived = onMessageArrived.bind(this)
      if (username != null) {
        options.userName = username
        options.password = password
        options.useSSL = useTLS// 如果使用 HTTPS 加密则配置为 true
      }
      this.mqtt.connect(options)

      console.log('mqtt', this)
    })
  }

  onConnect() {
    console.log('Connect Success', dayjs().format('MM-DD HH:mm:ss'))
  }

  async onConnectionLost() {
    console.error('mqtt连接断开', dayjs().format('MM-DD HH:mm:ss'))

    console.error(`开始尝试重连`, dayjs().format('MM-DD HH:mm:ss'))
    useBase().timeout = true
    useBase().connectLoading = true
    await this.checkLost()
    await this.lostMqttCb()
  }

  onMessageArrived(message) {
    console.log('origin message', message)
    this.cb(message)
  }

  checkLost() {  // 判断sessinoId是否过期  并判断网络状况
    let count = 1

    return new Promise((resolve, reject) => {
      if (!useUser().sessionId) {
        if (useBase().connectLoading) useBase().connectLoading = false
        throw Error('sessionId过期，请手动重连')
      }


      async function checkSession() {
        console.error(`查询session是否过期，第${count}次尝试，共5次`)

        let res
        try {
          res = await api.checkSession(useUser().sessionId)
        } catch (error) {
          count++

          if (count > 5) {
            ElMessage({
              message: '您的网络状况不佳，请检查网络后重试。',
              type: 'error',
              duration: 0
            })
            useBase().networkTimeout = true
            throw Error('用户网络不佳，终止重连')
          }

          setTimeout(async () => {
            await checkSession()
          }, 5000)
        }

        if (res) {
          if (!res.exits) {
            if (useBase().connectLoading) useBase().connectLoading = false
            throw Error('sessionId过期，请手动重连')
          } else {
            resolve(res)
          }
        }

      }

      checkSession()
    })
  }

}
