import { useUser } from '@/store/user'
import { useBase } from '@/store/base'
import { useMessage } from '@/store/message'
import api from '@/api'

/**
 * @description: 发送转人工埋点
 */
export async function sendGoLaborPoint() {
  const userStore = useUser()
  const baseStore = useBase()
  const msgStore = useMessage()

  await sendPoint({
    category: 'site',
    eventKey: 'clickIntelligentCustomerService'
  }, {
    deviceid: userStore.deviceId,
    time: new Date().getTime(),
    channel: String(userStore.channelId),
    applyTimeBegin: baseStore.inTime,
    applyCount: msgStore.list.filter(item => !item.history).length
  })
}

/**
 * @description: 页面曝光埋点
 */
export async function sendPageExposurePoint() {
  const userStore = useUser()

  await sendPoint({
    category: 'site',
    eventKey: 'getIntelligentCustomerService'
  }, {
    deviceid: userStore.deviceId,
    time: new Date().getTime(),
    channel: String(userStore.channelId),
  })
}

export async function sendPoint(params: {
  category: string
  eventKey: string
}, defineParams?: object) {
  const par = getParam(params, defineParams)
  const res = await api.sendPoint([par])
  console.log('埋点res', res)
}

function getParam(params, defineParams?) {
  const userStore = useUser()

  const session = 'yangcong345' + new Date().getTime() + Math.random().toString(36).slice(2)
  const currentTime = new Date().getTime()
  let omvd = localStorage.getItem('wh_crm_omvs')
  if (!omvd) {
    omvd = session
    localStorage.setItem('wh_crm_omvs', session)
  }

  const pointParam = {
    u_role: 'agent', // 固定值
    platform: 'telesale', // 固定值
    productId: '600', // 固定值
    os: 'pc', // 固定值
    channel: 4, // ???
    omvd: omvd, // 设备信息：每次登录进页面时的标识，包括刷新
    browserKernel: navigator.userAgent.toLowerCase(),
    browserName: 'chrome',
    session: session,
    u_user: String(userStore.uid), // 用户ID：与坐席ID保持一致
    eventTime: currentTime, // 触发埋点客户端当前时间，不可为空，时间戳
    d_app_version: '1.0.0',
    d_os_version: '1.0.0',
    uuid: currentTime + Math.random().toString(36).slice(2),
    category: params.category, // 类目
    eventKey: params.eventKey, // 埋点对象，不可为空
    ...defineParams
  }

  return pointParam
}
