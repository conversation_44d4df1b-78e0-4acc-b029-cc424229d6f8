import { useMessage } from '@/store/message'
import { isInNative } from '@guanghe-pub/onion-utils'
import { APPHelpCenterList } from '@/const/channel'
import { useUser } from '@/store/user'
import { useConfig } from '@/store/config'
import { replaceUrlHost } from '@kefuweb/utils'

/**
 * @description: 排除html标签字符，将其余字符中的超链接转化为a标签，并恢复html标签
 * @param {string} input
 */
export function convertStringWithLinks(input:string, notProcessLink = false) {
  const matchList:any = []
  let labelFlag = false

  function delUnCloseTag(str) { // 匹配未闭合的img和video标签
    const reg = /<(img|video|a)[^>]*?(?=(<|$))/
    const target = str.replace(reg, match => {
      labelFlag = true
      useMessage().labelLoading = true
      return ''
    })
    if (!labelFlag) useMessage().labelLoading = false

    return target
  }

  function delUnCloseMarkdown(str) {
    const reg = /\[([^\]]*?)\](?:\([^)]*(?=[^\)]*$)|$)|\[[^\]]*$/g
    const target = str.replace(reg, match => {
      labelFlag = true
      useMessage().labelLoading = true
      return ''
    })
    if (!labelFlag) useMessage().labelLoading = false

    return target
  }


  function saveMatch(match) {
    const tagIndex = matchList.length
    matchList.push(match)
    return `@@${tagIndex}@@`
  }

  function coverHTMLTagA(str) {
    const tagReg = /<a[^>]*>([^<]+)<\/a>/g
    return str.replace(tagReg, match => {
      return saveMatch(match)
    })
  }

  function coverHTMLTags(str) { // 在原字符中使用特殊标记标记排除的HTML标签
    const tagReg = /<[^>]*>/g
    return str.replace(tagReg, match => {
      return saveMatch(match)
    })
  }

  function coverHTMLTagImgToElImage(str) {
    const tagReg =  /<img\s+[^>]*src="([^"]*)"[^>]*style="([^"]*)"[^>]*>/gi
    return str.replace(tagReg, (match, src, style) => {
      useMessage().imgListPush(src)
      const target = `<img src="${src}" onClick="window.showImagePreview('${src}')" style="${style}" />`
      return saveMatch(target)
    })
  }

  function convertMarkdownImagesToHTML(str) { // 匹配![https](https)这种md格式并返回img标签
    const markdownImageRegex = /!\[([^\]]+)\]\(((https|ycmath)?:\/\/\S+?)\)/g
    return str.replace(markdownImageRegex, (match, $1, $2) => {
      useMessage().imgListPush($2)
      return saveMatch(`<img src="${$2}" onClick="window.showImagePreview(${$2})" alt="${$1}" />`)
    })
  }

  function convertMarkdownLinkToHTML(str) { // 匹配md格式的链接并返回a标签 [链接文字](网址链接)
    const markdownImageRegex = /\[([^\]]+)\]\(((https|ycmath)?:\/\/[^\s)]+)\)/g
    return str.replace(markdownImageRegex, (match, $1, $2) => {
      return saveMatch(`<a href="${$2}">${$1}</a>`)
    })
  }

  function convertLinksToAnchorTags(str) {  // 匹配纯文本的链接返回a标签
    // const urlRegex = /https?:\/\/[^\s/$.?#]+\.[^\s]+/g
    const urlRegex = /((https|ycmath)?:\/\/\S+)\b/g
    return str.replace(urlRegex, function(match) {
      if (/\.(jpg|jpeg|png|gif)$/i.test(match)) {
        useMessage().imgListPush(match)
        return saveMatch(`<img src="${match}" onClick="window.showImagePreview(${match})" />`)
      } else {
        if (notProcessLink) {
          return match
        } else {
          return saveMatch('<a href="' + match + '">' + match + '</a>')
        }
      }
    })
  }

  function restoreMatchTags(str) {
    return str.replace(/@@(\d+)@@/g, (match, tagIndex) => {
      return matchList[tagIndex] || ''
    })
  }

  function matchMiddleUrl(str) { // 过滤中间页的a标签
    if (useConfig().showMiddleJumptelsale) {
      return str
    }

    // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
    let tempDiv = document.createElement('div')
    tempDiv.innerHTML = str

    // 获取所有的 <a> 元素
    let linkElements = tempDiv.querySelectorAll('a')

    linkElements.forEach(item => {
      const href = item.getAttribute('href')
      if (href?.includes('middle/jumptelsale')) {
        item.remove()
      }
    })

    // 获取修改后的字符串
    let modifiedString = tempDiv.innerHTML

    return modifiedString
  }

  // 匹配lsy链接，如果是平板渠道就跳转平板树洞
  function matchLsy(str) {
    const PadChannel = ['dcac7bf0-0078-4f70-a867-091f84599cb7', 'ca10a17c-1120-40b9-b1e0-1c836f9f2873', '3f189b6a-43db-48ec-b6f4-f0c02cf6c566']
    let tempDiv = document.createElement('div')
    tempDiv.innerHTML = str

    // 获取所有的 <a> 元素
    let linkElements = tempDiv.querySelectorAll('a')

    linkElements.forEach(item => {
      const href = item.getAttribute('href')
      if (href?.includes('lsy') && PadChannel.includes(useUser().channelUniqueId)) {
        item.href = `${location.origin}/lsy/TreeHolePad?channelFrom=IM&${href.split('?')[1]}`
      }
    })

    // 获取修改后的字符串
    let modifiedString = tempDiv.innerHTML

    return modifiedString
  }

  // todo: 正则匹配中间页a标签

  let result = ''

  result = delUnCloseTag(input)
  result = delUnCloseMarkdown(result)
  result = coverHTMLTagA(result)
  result = coverHTMLTagImgToElImage(result)
  result = coverHTMLTags(result)
  result = convertMarkdownImagesToHTML(result)
  result = convertMarkdownLinkToHTML(result)
  result = convertLinksToAnchorTags(result)
  result = restoreMatchTags(result)

  // 使用hostReplace函数替换URL中的host
  result = replaceUrlHost(result)
  result = matchMiddleUrl(result)
  result = matchLsy(result)
  result = checkYcmathRoute(result)
  return result
}

function checkYcmathRoute(str) {
  const userStore = useUser()
  // 端内环境 || 公众版app渠道 不展示a标签  && 千人千面
  const unShowTag = !(isInNative() && (APPHelpCenterList.includes(userStore.channelUniqueId)) && userStore.checkShowClickEnable)

  // 创建一个临时 div 元素，将原始字符串作为其 innerHTML
  let tempDiv = document.createElement('div')
  tempDiv.innerHTML = str

  // 获取所有的 <a> 元素
  let linkElements = tempDiv.querySelectorAll('a')

  linkElements.forEach((linkElement) => {
    const href = linkElement.getAttribute('href')
    linkElement.style.color = '#1989fa'
    linkElement.removeAttribute('target')
    if (useConfig().checkPortalList(href)) { // 包含ycmath的为端内链接
      const browserJumpParam = {
        url: useUser().splitPortal(href),
        business: { fromPageName: 'kf-IM', from: 'kf-IM' }
      }
      linkElement.setAttribute('onClick', `browserJump(${JSON.stringify(browserJumpParam)})`)
      linkElement.removeAttribute('href')

      if (unShowTag) linkElement.remove()
    }
  })

  // 获取修改后的字符串
  let modifiedString = tempDiv.innerHTML

  return modifiedString
}
