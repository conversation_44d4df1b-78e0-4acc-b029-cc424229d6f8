import { BuryPoint } from '@guanghe-pub/onion-utils'
import axios from 'axios'
/**
 * https://guanghe.feishu.cn/sheets/shtcnTP5rhn8AJIpuQwCLJPACIc?sheet=b7c3e3
 */
const points = {
  enterCustomerServiceIMAbnormalInterface: {
    category: 'activity',
    data: ['failReason', 'errorCode', 'errorMessage'],
    desc: '基础服务异常报错',
  },
}

export default new BuryPoint(points, {
  env: import.meta.env.VITE_GETHOST,
  axios
})
