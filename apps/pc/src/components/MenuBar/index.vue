<template>
  <div class="px-20px menu-container flex justify-start align-center overflow-y-auto">

    <el-button v-if="laborStore.showLaborBtn"
               class="b-none button mr-12px"
               :disabled="!baseStore.initCompleted"
               :loading="laborStore.goLaborLoading"
               @click="laborStore.goLabor">
      <span class="c-#666 text-12px font-400">人工服务</span>
    </el-button>

    <div v-for="(item, index) in menuStore.menuListFilter"
         :key="index"
         class="mr-12px">
      <van-popover v-model:show="item.show"
                   placement="top"
                   :show-arrow="getActions(item.menus).length > 0"
                   :actions="getActions(item.menus)"
                   @select="e => openUrl(replaceUrlHost(e.menuDescription.content), '_self')"
                   @closed="active=-1">
        <template #reference>
          <div class="button flexD"
               :class="{active: active === index}"
               @click="clickMenu(item, index)">
            <span class="c-#666 text-12px font-400">{{ item.name }}</span>
          </div>
        </template>
      </van-popover>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { useLabor } from '@/store/labor'
import { useBase } from '@/store/base'
import openUrl from '#/script/openUrl'
import { useMenu } from '@/store/menu'
import { replaceUrlHost } from '@kefuweb/utils'

const laborStore = useLabor()
const baseStore = useBase()
const menuStore = useMenu()
const active = ref<number>(-1)

const getActions = (arr: any) => {
  return arr?.map(item => {
    return {
      text: item.name,
      ...item
    }
  })
}

/**
 * 点击菜单项处理函数
 * @param {any} row - 菜单项数据
 * @param {number} index - 菜单项索引
 */
const clickMenu = (row: any, index: number) => {
  if (row.menuDescription.content) {
    // 使用hostReplace函数替换URL中的host
    const replacedUrl = replaceUrlHost(row.menuDescription.content)
    openUrl(replacedUrl, '_self')
    row.show = false
    return
  }

  active.value = index
}
</script>

<style lang="scss" scoped>
.menu-container {
  .button {
    height: 27px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #999999;
    background: white;
    padding: 0 12px;
    cursor: pointer;
  }

  .active {
    background: unset;
    border: 1px solid #2CABFB;
    span {
      color: #2CABFB;
    }
  }
}
</style>
