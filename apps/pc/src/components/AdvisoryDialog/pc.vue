<!--
 * @Date         : 2024-03-29 11:12:07
 * @Description  : 咨询弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="advisory-dialog">
    <el-dialog v-model="laborStore.showTelsalePop"
               append-to="#IM"
               class="w-365px! h-300px rounded-12px! m-0!">
      <template #header>
        <div class="c-#333 text-18px line-height-25px font-bold">请选择咨询类型</div>
      </template>

      <div class="c-#333 text-15px line-height-24px mb-19px">
        为了更加高效解决您的问题，请选择您需要咨询的问题类型
      </div>

      <div class="content-box">
        <div @click="goMiddle">
          <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/assets/AdvisoryDialog/courses-5bdf1c00fbbe9627fe3e82c4b9d36098__w.png"
                          alt="购课咨询" />
          <span>购课咨询</span>
        </div>

        <div @click="goLabor();laborStore.showTelsalePop=false">
          <common-imgload src="https://fp.yangcong345.com/middle/1.0.0/assets/AdvisoryDialog/business-440e304ff381f25c59e371de98f5c7c2__w.png"
                          alt="业务咨询" />
          <span>业务咨询</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang='ts' setup>
import { useLabor } from '@/store/labor'
import { goMiddle, goLabor } from './jump'

const laborStore = useLabor()
</script>

<style scoped lang='scss'>
.advisory-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 36px 30px 36px !important;
  }
  :deep(.el-overlay-dialog) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.content-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  >div {
    width: 124px;
    height: 142px;
    border-radius: 4px;
    border: 1px solid #297BF1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    :deep(img) {
      width: 70px;
      height: 72px;
      margin-bottom: 16px;
    }
    span {
      color: #297BF1;
      font-size: 15px;
    }
  }
}
</style>
