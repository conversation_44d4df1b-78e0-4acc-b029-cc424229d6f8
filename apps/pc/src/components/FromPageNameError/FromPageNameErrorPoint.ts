import { BuryPoint } from '@guanghe-pub/onion-utils'
import axios from 'axios'
/**
 * https://guanghe.feishu.cn/sheets/shtcnTP5rhn8AJIpuQwCLJPACIc?sheet=b7c3e3
 */
const points = {
  getCustomerServiceIMTransition: {
    category: 'activity',
    desc: 'FromPageName异常报警中间页曝光',
  },
  clickCustomerServiceIMTransitionButton: {
    category: 'activity',
    desc: '异常报警中间页点击跳转帮助中心的按钮',
  }
}

export default new BuryPoint(points, {
  contextData: {
    productId: '419',
  },
  env: import.meta.env.VITE_GETHOST,
  axios
})
