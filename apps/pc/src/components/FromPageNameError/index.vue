<!--
 * @Date         : 2024-07-22 11:02:49
 * @Description  : FromPageNameError
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="error-container h-100vh w-100vw flex items-center justify-around flex-col fixed top-0 bg-white z-999">
    <h1>当前页面不可用</h1>
    <el-button type="primary"
               size="large"
               @click="goHelp">跳转帮助中心</el-button>
  </div>
</template>

<script lang='ts' setup>
import { openUrl } from '@kefuweb/utils'
import FromPageNameErrorPoint from './FromPageNameErrorPoint'

async function goHelp() {
  await FromPageNameErrorPoint.h5Post('clickCustomerServiceIMTransitionButton')
  openUrl(`${import.meta.env.VITE_HOST}/kefu-web-pc/help`, '_self')
}

onMounted(() => {
  FromPageNameErrorPoint.h5Post('getCustomerServiceIMTransition')
})
</script>

<style scoped lang='scss'>
</style>
