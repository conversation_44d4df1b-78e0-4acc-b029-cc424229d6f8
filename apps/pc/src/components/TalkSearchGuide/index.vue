<!--
 * @Date         : 2023-11-13 11:06:54
 * @Description  : 输入词条检索引导
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div ref="GudieRef"
       :class="[type === 'mobile' ? 'w-100% left-0' : 'w-50% shadow-[var(--el-box-shadow-light)]']"
       class="guide-container rounded-4px absolute bg-white z-99">
    <div v-for="(item, index) in guideList"
         :key="index"
         class="p-10px cursor-pointer"
         :class="[index !== guideList.length - 1 ? 'b b-b b-b-#D7EAFF b-b-solid' : '']">
      <el-text truncated
               @click="send(item.qandaUniqueId, item.question)"
               v-html="searchHighLight(item.question)" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import api from '@/api'
import { SearchGuideItem } from '@/api/modules/talk'
import { useModel } from '@/hooks/useBasic'

const props = defineProps<{
  text: string
  sendMsg: Function
  type: string
}>()
const emit = defineEmits(['update:text'])
const _text = useModel(props, emit, 'text')

const guideList = ref<SearchGuideItem[]>([])

async function getGuide(text) {
  const res = await api.searchGuide(text)

  if (props.text.length >= 2) {
    guideList.value = res.list
    calcTop()
  }
}

const GudieRef = ref()
const top = ref('0')
function calcTop() {
  nextTick(() => {
    let topVal = GudieRef.value.offsetHeight
    if (props.type === 'pc') topVal = topVal - 20
    top.value = `-${topVal}px`
  })
}

watch(() => props.text, (newV) => {
  const val = newV.trim()
  if ((hasEmoji(val) && val.length > 2) || (!hasEmoji(val) && val.length >= 2)) {
    getGuide(val)
  } else {
    guideList.value.length = 0
  }

})

function searchHighLight(str:string) {  // 搜索高亮
  // 使用<span>标签标记指定字符
  const targetStr = props.text.replace(/(\++)/g, '\\$1').trim() // 处理+字符，避免+被识别为量词
  let markedString = str.replace(new RegExp(targetStr, 'gi'), function(match) {
    return '<span style="color: #2CABFB;">' + match + '</span>'
  })

  return markedString
}

function send(id, msg) {
  api.countGuide({
    question: msg,
    clickType: 'search',
    qandaUniqueId: id
  })
  props.sendMsg(id, msg)
  guideList.value.length = 0
  _text.value = ''
}


function hasEmoji(str) {
  const emojiRegex = /[\uD800-\uDBFF][\uDC00-\uDFFF]|[\u2600-\u27FF]|[\u2B00-\u2BFF]|[\u2800-\u28FF]|[\u2700-\u27BF]|[\u1F300-\u1F5FF]|[\u1F680-\u1F6FF]|[\u200D\u2600-\u27FF\u2B00-\u2BFF\u2800-\u28FF\u2700-\u27BF\u1F300-\u1F5FF\u1F680-\u1F6FF]+/g
  return emojiRegex.test(str)
}
</script>

<style scoped lang='scss'>
.guide-container {
  top: v-bind(top)
}
</style>
