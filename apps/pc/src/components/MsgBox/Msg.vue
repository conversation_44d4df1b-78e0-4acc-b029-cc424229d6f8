<!--
 * @Date         : 2023-12-27 15:25:36
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-for="(item, index) in msgList"
       :key="index"
       :class="[item.me ? 'justify-end' : 'justify-start']"
       class="w-100% flex my-10px">
    <div v-if="item.type !== 'date'"
         class="max-w-80% flex flex-col"
         :class="[item.me ? 'mr-20px items-end' : 'ml-20px items-start']">

      <!-- 文字消息回复 -->
      <div v-if="item.type === 'txt'"
           :class="[item.me ? 'txt-me' : 'txt-other']"
           class="p-10px w-fit english-warp"
           v-html="item.msg" />

      <!-- 富文本消息回复及关联问题 -->
      <div v-if="item.type === 'qanda'"
           class="txt-other p-10px w-100%">
        <div class="mb-15px break-words img-box"
             :class="{'underline-content': item.content?.qandaRels?.length !== 0}"
             v-html="item.content.answer" />
        <div v-if="item.content?.qandaRels?.length > 0">
          <div class="text-14px">{{ baseStore.relatedGuideSentence }}</div>
          <el-link v-for="(question, dx) in item.content.qandaRels"
                   :key="dx"
                   :underline="false"
                   :disabled="checkLinkDisable(item)"
                   class="my-5px block!"
                   type="primary"
                   @click="sendTmpMsg(question.relatedQandaUniqueId, question.qandaBaseQuestion)">{{ `${dx + 1}、${question.qandaBaseQuestion}` }}</el-link>
        </div>
      </div>

      <!-- 文字消息+引导问题 -->
      <div v-if="item.type === 'similarAndTxt'"
           class="txt-other">
        <div class="p-10px">
          <div class="mb-10px"
               :class="{'underline-content': item.content?.qandaRels?.length !== 0 && item.content.length > 0 && (!baseStore.streamLoading || index !== msgList.length - 1)}">
            <span class="english-warp similar-text-content"
                  v-html="item.msg" />
            <CursorLoading v-if="baseStore.streamLoading && (index + 1 === msgList.length) && !messageStore.labelLoading" />
            <MsgLoading v-if="messageStore.labelLoading && (index + 1 === msgList.length)"
                        class="inline-flex" />
          </div>
          <div v-if="item.content.length > 0 && (!baseStore.streamLoading || (index + 1 !== msgList.length))"
               class="similar">
            <div v-if="!item.content.unsearchable">{{ baseStore.guideSentence }}</div>
            <el-link v-for="(question, dx) in item.content"
                     :key="dx"
                     :underline="false"
                     :disabled="checkLinkDisable(item)"
                     class="my-5px block c-#2CABFB!"
                     @click="messageStore.countGuide(question.qandaUniqueId, question.baseQuestion, index); sendTmpMsg(question.qandaUniqueId, question.baseQuestion)">{{ `${dx + 1}、${question.baseQuestion}` }}</el-link>
          </div>
        </div>
        <PraiseStomp v-if="(!baseStore.streamLoading || (index + 1 !== msgList.length)) && !item.history && !item.origin.content?.unsearchable"
                     :is-last="index === msgList.length - 1"
                     :msg-id="item.msgId" />

      </div>

      <!-- 消息撤回 -->
      <div v-if="item.type === 'revoke' || item.revoke"
           class="txt-other p-10px bg-#CEE6FF! revoke w-fit!">
        该条消息已撤回
      </div>
    </div>

    <!-- 时间 -->
    <div v-if="item.type === 'date'"
         class="w-100% flexD">
      <span class="bg-#D3E7FB text-12px c-white px-5px py-2px">{{ item.date }}</span>
    </div>

    <!-- 离线留言 -->
    <div v-if="item.type === 'offline'"
         class="offline-pc">
      <div class="flex items-center">
        <el-icon color="#2CABFB"
                 size="12px"><ChatDotRound /></el-icon>
        <span class="c-#2CABFB text-12px ml-3px">离线留言</span>
      </div>
      <div class="bg-#F5FBFF px-16px py-13px text-12px c-#666 mt-6px font-300 line-h-19px">
        <span v-if="item.msgTyp === 'text'">{{ item.msg }}</span>
        <img v-if="item.msgTyp === 'image'"
             :src="item.msg"
             class="max-w-100%">
        <video v-if="item.msgTyp === 'video'"
               :src="item.msg"
               class="max-w-100%" />
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useBase } from '@/store/base'
import MsgLoading from '@/components/MsgLoading/index.vue'
import CursorLoading from '@/components/MsgLoading/Cursor.vue'
import PraiseStomp from './children/PraiseStomp/index.vue'
import { ChatDotRound } from '@element-plus/icons-vue'
import { useMessage } from '@/store/message'
import { useEnv } from '@/store/env'

const props = defineProps(['msgList', 'sendTmpMsg'])

const envStore = useEnv()
const messageStore = useMessage()
const baseStore = useBase()

function checkLinkDisable(info) {
  return (baseStore.timeout || baseStore.renew || info.history)
}
</script>

<style scoped lang='scss'>
@import url('./txt.scss');

.rich-text-box {
  :deep(p) {
    margin: 0;
  }
}

.revoke::before {
  border-right: 6px solid #CEE6FF !important;
}

.similar {
  a {
    display: block;
  }
}

.english-warp {
  word-wrap: break-word;
  white-space: pre-line;
}

.underline-content {
  position: relative;
  padding-bottom: 10px;
  &::after {
    content: '';
    height: 1px;
    background-color: #D7EAFF;
    width: calc(100% + 20px);
    display: inline-block;
    position: absolute;
    bottom: 0;
    left: -10px;
  }
}

.similar-text-content {
  :deep(img) {
    max-width: 100%;
  }
}

.img-box {
  :deep(img) {
    max-width: 100%;
  }
}

.offline-pc {
  width: calc(100% - 40px);
  word-break: break-all;
}

.offline-mobile {
  width: 80%;

}
</style>
