<!--
 * @Date         : 2023-09-25 17:31:51
 * @Description  : 猜你想问
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="info"
       class="relative">
    <div class="h-36px bg-white pt-5px flex items-center justify-end rounded-t-6px">
      <div class="flex justify-between items-center w-100% px-15px">
        <div class="flexD">
          <common-imgload class="w-32px mr-2px"
                          src="https://fp.yangcong345.com/middle/1.0.0/assets/guess/question-929bc7dc786f22e1f285066d8ebc00a3__w.png" />
          <div class="font-600 c-black text-16px">{{ info?.firstSentence }}</div>
        </div>

        <el-button v-if="nowGroup.qandaSet?.length > 5"
                   link
                   type="info"
                   @click="calcQandaSet()">
          <el-icon color="#2CABFB"><Refresh /></el-icon>
          <span class="text-12px c-#2CABFB">换一批</span>
        </el-button>
      </div>
    </div>

    <div class="px-10px bg-white pb-5px tab-box rounded-b-6px">
      <van-config-provider :theme-vars="{
        'tabsBottomBarHeight': '2px',
        'tabsBottomBarColor': '#2CABFB'
      }">
        <van-tabs v-model:active="nowGroupName"
                  shrink>
          <van-tab v-for="(group, idx) in info?.solutionSet"
                   :key="idx"
                   :name="idx"
                   :title="group.name">
            <el-link v-for="(question, dx) in computedGroup()"
                     :key="dx"
                     :underline="false"
                     :disabled="checkLinkDisable(info)"
                     class="link w-100%"
                     @click="sendTmpMsg(question.qandaUniqueId, question.baseQuestion)">
              <div class="py-8px flex justify-between w-100% b-b-#F1F9FF b-b-solid b-b-1"
                   :class="{'b-b-0!': dx === (computedGroup().length - 1)}">
                <span class="c-#666">
                  <span class="mr-5px text-16px font-600 italic"
                        :class="`number-${dx+1}`">{{ dx + 1 }}</span>
                  {{ question.baseQuestion }}</span>
                <el-icon color="#333"><ArrowRight /></el-icon>
              </div>
            </el-link>
          </van-tab>
        </van-tabs>
      </van-config-provider>

    </div>
  </div>
</template>

<script lang='ts' setup>
import useQandAGroup from './hooks/useQandAGroup'
import { Refresh, ArrowRight } from '@element-plus/icons-vue'
import { useBase } from '@/store/base'

const baseStore = useBase()
const props = defineProps<{
  checkLinkDisable: Function
  sendTmpMsg: Function
}>()

const info = computed(() => {
  return baseStore.solution
})

const nowGroupName = ref(0)
const nowGroup = computed(() => {
  return info.value?.solutionSet[nowGroupName.value] ?? []
})
const { computedGroup, calcQandaSet } = useQandAGroup(nowGroup)
</script>

<style scoped lang='scss'>
.link {
  :deep(.el-link__inner) {
    @include textHideLine1;
    width: 100%;
    justify-content: flex-start;
  }
}

.tab-box {
  :deep(.van-tabs__wrap) {
    position: relative;
    overflow: initial;
    &::after {
      content: '';
      height: 1px;
      background-color: #D7EAFF;
      width: calc(100% + 20px);
      display: inline-block;
      position: absolute;
      bottom: 0;
      left: -10px;
    }
    .van-tab--active {
      .van-tab__text {
        color: #2CABFB;
      }
    }
    .van-tab__text {
      color: black;
    }
  }
  :deep(.van-tabs__content) {
    margin-top: 10px;
  }
}

.number-1 {
  color: #F94D46
}
.number-2 {
  color: #F96F47
}
.number-3 {
  color: #FB9D43
}
.number-4, .number-5 {
  color: #BBBBBB
}
</style>
