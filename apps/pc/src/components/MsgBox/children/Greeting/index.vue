<!--
 * @Date         : 2023-09-25 16:05:36
 * @Description  : 欢迎语
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flexD px-20px">
    <!-- <div class="flex items-center justify-between greeting-box relative">
      <div class="c-white text-15px font-400 absolute top-33px left-67px">{{ baseStore.firstSentence }}</div>
    </div> -->
    <div class="bg-white w-100% flex items-center p-10px rounded-6px">
      <common-imgload class="w-60px h-60px mr-10px"
                      src="https://fp.yangcong345.com/middle/1.0.0/greeting_icon-423e79fbb9de67bf7e2f095c5e64641f__w.png" />

      <div>
        <div class="c-#2CABFB font-600 text-16px mb-10px">Hi，我是AI智能客服葱宝，请问你遇到了什么问题呢？快快问我吧~</div>
        <div class="c-#666 text-12px font-300">{{ baseStore.firstSentence }}</div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useBase } from '@/store/base'

const baseStore = useBase()
</script>

<style scoped lang='scss'>
</style>
