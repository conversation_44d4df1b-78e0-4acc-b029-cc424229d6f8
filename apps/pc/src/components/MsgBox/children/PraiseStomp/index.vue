<!--
 * @Date         : 2024-04-17 18:05:49
 * @Description  : 点赞踩
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <van-divider :style="{borderColor: '#D8D8D8',marginTop: '10px',marginBottom: 0}" />

    <div class="flexD h-35px">
      <div class="btn-box"
           :class="{active: active === 'good'}"
           @click="changeActive('good')">
        <common-imgload v-if="active === 'good'"
                        src="https://fp.yangcong345.com/middle/1.0.0/assets/PraiseStomp/happy_active-98bf0f94c826fc5aa7dd79ec3a3ebdd8__w.png"
                        alt="赞" />
        <common-imgload v-else
                        src="https://fp.yangcong345.com/middle/1.0.0/assets/PraiseStomp/happy-effa4c260de588679e016a28a6ed31f6__w.png"
                        alt="赞" />
        <div>有用</div>
      </div>

      <van-divider vertical
                   :style="{borderColor: '#D8D8D8'}"
                   class="h-100%!" />

      <div class="btn-box"
           :class="{active: active === 'bad'}"
           @click="changeActive('bad')">
        <common-imgload v-if="active === 'bad'"
                        src="https://fp.yangcong345.com/middle/1.0.0/assets/PraiseStomp/sad_active-a8e436d79368aa1696a9bf79f2db4346__w.png"
                        alt="踩" />
        <common-imgload v-else
                        src="https://fp.yangcong345.com/middle/1.0.0/assets/PraiseStomp/sad-b175d58e8c65d4a1fc87624f6ec73775__w.png"
                        alt="踩" />
        <div>没用</div>
      </div>
    </div>

    <StompForm v-if="active === 'bad' && !submited"
               v-model:submited="submited"
               :msg-id="msgId" />
  </div>
</template>

<script lang='ts' setup>
import StompForm from './StompForm.vue'
import api from '@/api'

const props = defineProps<{
  msgId: string
  isLast: boolean
}>()

const active = ref('')

const submited = ref(false)

async function changeActive(state: string) {
  if (active.value) return
  await api.replyComment({
    instance: {
      id: props.msgId,
      commentType: state
    }
  })
  active.value = state
  if (state === 'bad' && props.isLast) {
    scrollBottom()
  }
}

function scrollBottom() {
  nextTick(() => {
    const msgBox = document.querySelector('#msgBox')
    if (msgBox) {
      msgBox.scrollTop = msgBox?.scrollHeight
    }
  })
}
</script>

<style scoped lang='scss'>
.active {
  > div {
    color: #009CFF;
  }
  :deep(img) {
    height: 23px !important;
    width: 35px !important;
  }
}

.btn-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
  padding: 10px 0;
  cursor: pointer;

  :deep(img) {
    height: 21px;
    width: 24px;
    margin-right: 10px;
  }

  > div {
    color: #333333;
    font-size: 600;
  }
}
</style>
