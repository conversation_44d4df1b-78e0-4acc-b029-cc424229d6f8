<!--
 * @Date         : 2024-04-17 18:39:16
 * @Description  : 没用表单
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <van-divider :style="{borderColor: '#D8D8D8',marginTop: 0,marginBottom: 0}" />

    <div class="p-15px question-box">
      <div class="flex items-center flex-wrap">
        <div v-for="(question, index) in questionList"
             :key="index"
             :class="[chooseQuestion===question?'active':'question']"
             class="mr-5px mb-10px"
             @click="chooseQuestion=question">{{ question }}</div>
      </div>

      <van-field v-model="message"
                 class="bg-#F5F5F5! b b-#E3E3E3 b-solid rounded-10px"
                 rows="2"
                 type="textarea"
                 maxlength="300"
                 placeholder="说说详细原因帮助我们改善" />

      <van-button color="#009DFF"
                  round
                  class="w-100% mt-10px! h-35px!"
                  @click="submit">提交</van-button>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import api from '@/api'

const props = defineProps<{
  msgId: string
}>()

const submited = defineModel('submited')

const questionList = ['没有回答我的问题', '答案太差不好看', '看不懂', '试过了没用']

const chooseQuestion = ref('')
const message = ref('')

async function submit() {
  await api.replyComment({
    instance: {
      id: props.msgId,
      label: chooseQuestion.value,
      comment: message.value,
      commentType: 'bad'
    }
  })
  submited.value = true
  ElMessage.success('提交成功，感谢您宝贵的建议～')
}
</script>

<style scoped lang='scss'>
.question {
  color: #999;
  border-radius: 20px;
  border: 1px solid #999;
  border-width: var(van-hairline--top);
  padding: 6px 10px;
  font-size: 14px;
  cursor: pointer;
}

.active {
  color: #009CFF;
  border-radius: 20px;
  border: 1px solid #009CFF;
  padding: 6px 10px;
  font-size: 14px;
  background-color: #EEF9FF;
}

.question-box {
  ::placeholder {
    color: #999;
  }
}
</style>
