<!--
 * @Date         : 2023-08-07 13:42:18
 * @Description  : 消息loading
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flex w-auto justify-center items-center pl-10px">
    <div class="loading loading-animation loading1" />
    <div class="loading loading-animation loading2" />
    <div class="loading loading-animation loading3" />
  </div>
</template>

<script lang='ts' setup>

</script>

<style scoped lang='scss'>
.loading {
  width: 10px;
  height: 10px;
  background: #409EFF;
  border-radius: 100px;
  margin-right: 5px;
}

.loading1 {
  animation-delay: 160ms;
}

.loading2 {
  animation-delay: 320ms;
}

.loading3 {
  animation-delay: 480ms;
}

.loading-animation {
  animation-name: loading-keyframes;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  transform-origin: center center;
}

@keyframes loading-keyframes {
  0% {
    transform: scale(0)
  }

  40% {
    transform: scale(1)
  }

  100% {
    transform: scale(0)
  }
}
</style>
