<!--
 * @Date         : 2023-12-06 18:57:02
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="h-18px inline-flex justify-center items-center">
    <span class="cursor" />
  </div>
</template>

<script lang='ts' setup>

</script>

<style scoped lang='scss'>
.cursor {
  height: 14px;
  width: 1px;
  background: #333;
  position: relative;
  top: 2px;

  animation-name: Cursor;
  animation-duration: 1s;
  animation-iteration-count: infinite;
}

@keyframes Cursor {
  0% {
    opacity: 1
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
