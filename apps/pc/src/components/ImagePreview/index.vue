<!--
 * @Date         : 2023-11-22 12:27:14
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-23 14:34:18
-->

<template>
  <div class="preview-box">
    <van-image-preview v-model:show="show"
                       :before-close="beforeClose"
                       :start-position="startPosition"
                       :images="messageStore.imgList"
                       closeable
                       @click="show = false" />
  </div>
</template>

<script lang='ts' setup>
import { useMessage } from '@/store/message'

const show = ref(false)
const messageStore = useMessage()
const startPosition = ref(0)


watch(show, newV => {
  if (newV) {
    startPosition.value = messageStore.imgList.findIndex(item => item === messageStore.chooseImg)
  }
})

function beforeClose(e) {
  show.value = false
}

onMounted(() => {
  window.showImagePreview = function(img:string) {
    messageStore.chooseImg = img
    show.value = true
  }
})
</script>

<style scoped lang='scss'>
.preview-box {
  :deep(.van-image-preview__swipe-item) {
    overflow: auto !important;
  }
}
</style>
