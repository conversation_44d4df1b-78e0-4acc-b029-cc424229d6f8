<!--
 * @Date         : 2023-11-03 16:46:24
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-11-09 10:45:00
-->

<template>
  <el-dialog v-model="errorStore.showErrorDialog"
             :close-on-click-modal="false"
             width="450px"
             title="网络异常"
             @open="popup()"
             @close="closeBtn(); errorStore.errorDialogReconnectBtn()">
    <div>当前网络环境异常，请选择点击以下按钮继续咨询</div>

    <template #footer>
      <div>
        <el-button type="primary"
                   @click="reconnectBtn(); errorStore.errorDialogReconnectBtn()">重新连接</el-button>
        <el-button v-if="laborStore.canGoLabor"
                   type="success"
                   @click="golaborBtn(); errorStore.errorDialogLaborBtn()">人工服务</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { useError } from '@/store/error'
import { useLabor } from '@/store/labor'
import usePoint from './usePoint'

const laborStore = useLabor()
const errorStore = useError()
const { reconnectBtn, closeBtn, golaborBtn, popup } = usePoint('pc')
</script>

<style scoped lang='scss'>

</style>
