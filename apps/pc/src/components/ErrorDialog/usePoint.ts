import { BuryPoint, getQueryObject } from '@guanghe-pub/onion-utils'
import axios from 'axios'
import { useLabor } from '@/store/labor'

/**
 * https://guanghe.feishu.cn/sheets/shtcnTP5rhn8AJIpuQwCLJPACIc?sheet=b7c3e3
 */

export default function(platform) {
  const points = {
    getCustomerServiceIMPopup: {
      category: 'activity',
      data: ['isSignUp'],
      desc: '客服IM弹窗页面曝光',
    },
    clickCustomerServiceIMPopup: {
      category: 'activity',
      data: ['button', 'isSignUp'],
      desc: '客服IM弹窗页面按钮点击',
    },
  }

  const point = new BuryPoint(points, {
    contextData: {
      productId: '419',
      platform
    },
    env: import.meta.env.VITE_GETHOST,
    axios
  })

  function handle(type, par: {}) {
    const laborStore = useLabor()
    const { userId } = getQueryObject()

    if (userId) {
      point.post(type, {
        isSignUp: laborStore.canGoLabor,
        ...par
      })
    } else {
      point.h5Post(type, {
        isSignUp: laborStore.canGoLabor,
        ...par
      })
    }
  }

  function reconnectBtn() {
    handle('clickCustomerServiceIMPopup', { button: '重新连接' })
  }

  function closeBtn() {
    handle('clickCustomerServiceIMPopup', { button: '关闭' })
  }

  function golaborBtn() {
    handle('clickCustomerServiceIMPopup', { button: '人工服务' })
  }

  function popup() {
    handle('getCustomerServiceIMPopup', { })
  }

  return { reconnectBtn, closeBtn, golaborBtn, popup }
}
