<!--
 * @Date         : 2023-11-03 16:47:09
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <van-dialog v-model:show="errorStore.showErrorDialog"
              title="网络异常"
              :close-on-click-overlay="true"
              message="当前网络环境异常，请选择点击以下按钮继续咨询"
              @open="popup()"
              @close="closeBtn(); errorStore.errorDialogReconnectBtn()">
    <template #footer>
      <div class="flex items-center w-100% justify-around pb-20px">
        <span v-if="laborStore.canGoLabor"
              @click="golaborBtn(); errorStore.errorDialogLaborBtn()">人工服务</span>
        <span class="c-#1989fa"
              @click="reconnectBtn(); errorStore.errorDialogReconnectBtn()">重新连接</span>
      </div>
    </template>

  </van-dialog>
</template>

<script lang='ts' setup>
import { useError } from '@/store/error'
import { useLabor } from '@/store/labor'
import usePoint from './usePoint'

const laborStore = useLabor()
const errorStore = useError()
const { reconnectBtn, closeBtn, golaborBtn, popup } = usePoint('h5')
</script>

<style scoped lang='scss'>

</style>
