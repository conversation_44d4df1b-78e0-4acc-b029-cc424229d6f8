import { defineStore } from 'pinia'
import { useUser } from './user'
import errorPoint from '@/script/errorPoint'
import { useLabor } from '@/store/labor'

interface ErrorState {
  showErrorDialog: boolean
}

export const useError = defineStore({
  id: 'Error',
  state(): ErrorState {
    return {
      showErrorDialog: false,
    }
  },
  getters: {
  },
  actions: {
    reconnectionNetwork() { // 网络断开重连
      useUser().mqtt.checkLost()
    },
    reconnectionMqtt() {  // mqtt断开重连
      useUser().mqtt.onConnectionLost()
    },
    errorDialogReconnectBtn() { // 网络环境异常弹窗 重新连接按钮
      useLabor().goRobot()
    },
    errorDialogLaborBtn() { // 网络环境异常弹窗 转人工按钮
      // useLabor().goLabor()
      useLabor().goRobot()  // 网络异常情况处理需求更改 https://project.feishu.cn/wuhan/story/detail/4878569511
    },
    checkChannelEmpty(channel) {
      if (!channel) {  // chanelId为0或空时进行刷新
        useLabor().goRobot()
        throw Error(`渠道错误。channel:${channel}`)
      }
    },
    basePortError(error) { // 初始化接口报错
      console.log(error)
      const baseUrlList = ['/web/token/check', '/web/session/connect']
      if (baseUrlList.includes(error?.config?.url) || error?.config?.url.includes('/web/config')) {
        errorPoint.h5Post('enterCustomerServiceIMAbnormalInterface', {
          failReason: error.response.data.reason,
          errorCode: error.response.data.code,
          errorMessage: error.response.data.message
        })
        useLabor().goRobot()
        throw Error('初始化失败')
      }
    },
  },
})
