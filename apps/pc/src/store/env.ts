import { defineStore } from 'pinia'
import { getQueryObject } from '@guanghe-pub/onion-utils'

interface EnvState {
  env: 'mobole' | 'pc' | ''
  isPad: boolean
}

export const useEnv = defineStore({
  id: 'ENV',
  state(): EnvState {
    return {
      env: '',
      isPad: false
    }
  },
  getters: {
    isPC(state): boolean {
      return this.env === 'pc'
    },
    isMobile(state): boolean {
      return this.env === 'mobole'
    },
  },
  actions: {
    getEnv() {
      const { isPad } = getQueryObject()

      if (isPad && isPad === 'true') {
        this.env = 'mobole'
        this.isPad = true
        return
      }

      if (location.pathname.includes('mobile')) {
        if (Math.min(document.documentElement.clientWidth, document.documentElement.clientHeight) >= 768) {
          this.isPad = true
        }
        this.env = 'mobole'
      } else {
        this.env = 'pc'
      }
    }
  },
})
