import { defineStore } from 'pinia'
import { MsgObj } from '@/types/mssage'
import api from '@/api'
import { useBase } from '@/store/base'
import { useUser } from './user'
import dayjs from 'dayjs'
import { OfflineMsg } from '@/api/modules/offline'

interface MessageState {
  list: MsgObj[]
  IDRecoveryMsgList: any[] // 回复序号快捷回复数组记录
  msgType: string
  imgList: string[] // 消息内所有图片消息list
  chooseImg: string  // 预览中图片的src

  countGuideMap: string[] // 记录本次会话引导点击的引导问题 避免重复计数
  labelLoading: boolean // 加载标签或md链接loading

  historyList: MsgObj[] // 历史消息
  offlineList: OfflineMsg[] // 离线消息

}

export const useMessage = defineStore({
  id: 'MESSAGE',
  state(): MessageState {
    return {
      list: [],
      IDRecoveryMsgList: [],
      msgType: '',
      imgList: [],
      chooseImg: '',

      countGuideMap: [],
      labelLoading: false,

      historyList: [],
      offlineList: []
    }
  },
  getters: {
  },
  actions: {
    setIDRecoveryMsgList(data) { // 如果有关联问题 就更新关联问题列表 回复序号进行回答
      this.msgType = data.msgType
      if (data.msgType === 'qanda') {
        this.IDRecoveryMsgList = data.content.qandaRels
      } else if (data.msgType === 'similar_and_txt') {
        this.IDRecoveryMsgList = data.content.similar
      } else if (data.msgType === 'similar') {
        this.IDRecoveryMsgList = data.content
      } else if (data.msgType === 'solutionGroup') {
        this.IDRecoveryMsgList = useBase().solution
      }
      console.log('回复序号可回答问题：', this.IDRecoveryMsgList)
    },
    checkIDRecoverMsg(str) {
      const IDList = [1, 2, 3, 4, 5]
      const index = Number(str)

      if (!this.IDRecoveryMsgList || this.IDRecoveryMsgList.length < index) {
        return false
      }

      if (IDList.includes(index)) {
        const { qandaUniqueId, baseQuestion, qandaBaseQuestion, relatedQandaUniqueId } = this.IDRecoveryMsgList[index - 1] ?? {}
        const id = this.msgType === 'qanda' ? relatedQandaUniqueId : qandaUniqueId
        const content = baseQuestion ?? qandaBaseQuestion

        if (!id || !content) {
          return false
        }

        return { id, content }
      }

      return false
    },
    imgListPush(src:string) {
      if (!this.imgList.includes(src)) {
        this.imgList.push(src)
      }
    },
    countGuide(id: string, msg: string, index: number) {  // 引导问题计数上报
      const countKey = `${index}-${id}`
      if (this.countGuideMap.includes(countKey)) return

      api.countGuide({
        question: msg,
        clickType: 'guide',
        qandaUniqueId: id
      })
      this.countGuideMap.push(countKey)
    },
    async getOffline() {
      if (!useUser().userId) return

      const res = await api.getOffline({
        userId: useUser().userId,
        sessionId: useUser().sessionId,
        endTime: dayjs(useBase().inTime).unix(),
        startTime: dayjs().subtract(7, 'day').unix()
      })
      this.offlineList = res.messages.reverse()

      const unreadList: any = this.offlineList.filter(item => {
        return item.read !== 'read'
      }).map(item => {
        return {
          type: 'offline',
          msg: item.content,
          msgType: item.msgType
        }
      })
      console.log('unreadList', unreadList)
      this.list.unshift(...unreadList)
    }
  },
})
