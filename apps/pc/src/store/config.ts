import { defineStore } from 'pinia'
import api from '@/api'
import { Portal } from '@/api/modules/portal'
import { getQueryObject } from '@guanghe-pub/onion-utils'
import { useUser } from './user'

interface ConfigState {
  portalList: Portal[]  // 传送门支持的链接列表

  showMiddleJumptelsale: boolean // 是否展示学习规划师（middle/jumptelsale）
}

export const useConfig = defineStore({
  id: 'Config',
  state(): ConfigState {
    return {
      portalList: [],

      showMiddleJumptelsale: true
    }
  },
  getters: {
  },
  actions: {
    async getPortal() {
      const res = await api.getPortal()
      this.portalList = res.list
    },
    checkPortalList(url) {
      return this.portalList.find(item => item.link === url)
    },
    async getUserLayered() {  // 判断用户分层
      const { userId, channelId } = getQueryObject()
      const helpChannel = ['be93287a-aa27-4d04-8e07-2ca8672e6045', '982151ce-919e-4915-a065-9e1f6567f0d6', 'e30b5dcb-ff61-4f2f-8f70-b881891a1134']
      console.log('innnn', helpChannel.includes(channelId), channelId)
      if (!helpChannel.includes(channelId)) {
        this.showMiddleJumptelsale = false
        return
      }

      if (userId) {
        const res = await api.userLayered({
          onionUserId: userId,
        })
        this.showMiddleJumptelsale = res.show
      } else {
        this.showMiddleJumptelsale = false
      }
    }
  },
})
