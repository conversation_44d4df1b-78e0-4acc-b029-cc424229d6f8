import { defineStore } from 'pinia'

interface BaseState {
  avatar: string  // 头像
  nickName: string  // 昵称
  firstSentence: string // 欢迎语
  expireSentence: string  // 过期语句
  expireTime: number  // 过期时间
  noServiceSentence: string // 无人工服务回复语句
  relatedGuideSentence: string  // 关联问题引导语
  guideSentence: string // 引导问题引导语

  noServiceBeginAt: string  // 无人工承接开始时间
  noServiceEndAt: string  // 无人工承接结束时间

  inTime: number  // 进入页面的时间戳 在进行mqtt连接之前

  networkTimeout: boolean // 网络超时状态
  timeout: boolean  // 超时状态
  connectLoading: boolean // 重连loading
  renew: boolean  // 重复打开状态
  loading: boolean  // 等待状态 加载中loading
  streamLoading: boolean  // 消息流持续loading
  renewStr: string  // 重发打开提醒语

  waitMsgLoading: boolean  // 发送消息到消息返回的loading

  loadingCompleted: boolean // 加载完成
  solution: any // 欢迎语卡片内容

  initCompleted: boolean  // 初始化完成flag mqtt初始化完成 允许发送消息
}

export const useBase = defineStore({
  id: 'BASE',
  state(): BaseState {
    return {
      avatar: '',
      nickName: '',
      firstSentence: '',
      expireSentence: '',
      expireTime: 0,
      noServiceSentence: '',
      relatedGuideSentence: '',
      guideSentence: '',

      noServiceBeginAt: '20:00:00',
      noServiceEndAt: '10:00:00',

      inTime: 0,

      timeout: false,
      connectLoading: false,
      renew: false,
      loading: false,
      streamLoading: false,
      renewStr: '',
      networkTimeout: false,

      waitMsgLoading: false,

      loadingCompleted: false,
      solution: null,

      initCompleted: false
    }
  },
  getters: {
  },
  actions: {
    setRobotBaseInfo(info:any) {
      this.avatar = info?.avatar
      this.nickName = info?.nickName
      this.firstSentence = info?.firstSentence
      this.expireSentence = info?.expireSentence
      this.expireTime = info?.expireTime
      this.noServiceSentence = info?.noServiceSentence
      this.relatedGuideSentence = info?.relatedGuideSentence
      this.guideSentence = info?.guideSentence

      this.noServiceBeginAt = info?.noServiceBeginAt
      this.noServiceEndAt = info?.noServiceEndAt
    },
    webReload() {
      location.reload()
    },
  },
})
