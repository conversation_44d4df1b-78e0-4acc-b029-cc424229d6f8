/*
 * @Date         : 2023-09-07 11:08:50
 * @Description  : 转人工相关逻辑仓库
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { defineStore } from 'pinia'
import { TransferLaborRes } from '@/api/modules/base'
import { LaborUrl, RobotUrl } from '@/const/sobotUrl'
import { sendGoLaborPoint } from '@/script/point'
import { useBase } from './base'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { getQueryObject, browserGETMe, isInNative, callNative, browserJump, browserTitle } from '@guanghe-pub/onion-utils'
import { useUser } from '@/store/user'
import api from '@/api'
import { useConfig } from './config'

interface LaborState {
  goLaborLoading: boolean

  changeAgent: 'on' | 'off'  // 是否开启人工显示
  delayChangeAgent: 'on' | 'off'  // 是否开启延迟显示人工
  delay: string  // 延迟次数
  messagePoint: string[]  // 关键词

  delayCount: number  // 对话轮次计数
  sendGoLaborMsg: Function

  canGoLabor: boolean // 当前是否可以转人工

  showTelsalePop: boolean  // 是否显示选择咨询类型弹窗

  lastGolaborTime: number  // 最后转人工时间
  isGoLaborBack: boolean  // 是否是三分钟内从人工返回的用户
}

export const useLabor = defineStore({
  id: 'LABOR',
  state(): LaborState {
    return {
      goLaborLoading: false,

      changeAgent: 'off',
      delayChangeAgent: 'off',
      messagePoint: [],
      delay: '0',

      delayCount: 0,
      sendGoLaborMsg: () => {},

      canGoLabor: true,

      showTelsalePop: false,

      lastGolaborTime: 0,
      isGoLaborBack: false
    }
  },
  getters: {
    showLaborBtn(state: LaborState):boolean {
      if (this.getChangeAgent) {
        return true
      }

      if (this.getDelayChangeAgent && (this.delayCount >= this.getDelay)) {
        return true
      }

      return false
    },
    getChangeAgent(state: LaborState): boolean {
      return this.changeAgent === 'on'
    },
    getDelayChangeAgent(state: LaborState): boolean {
      return this.delayChangeAgent === 'on'
    },
    getDelay(state: LaborState): number {
      return Number(this.delay)
    },
  },
  actions: {
    setTransferLabor(par: TransferLaborRes) {  // 设置转人工相关参数
      this.changeAgent = par.changeAgent
      this.delayChangeAgent = par.delayChangeAgent
      this.delay = par.delay
      this.messagePoint = par.messagePoint
    },
    checkMessagePointGoLabor(msg:string) {  // 关键词转人工
      const THAT = this
      return new Promise(async (resolve, reject) => {
        const replaceMsg = msg.replace(/[ \p{P}]/gu, '')

        if (this.messagePoint.includes(replaceMsg)) {

          if (this.delayCount === 0) {
            await THAT.goLabor({
              skipTelsalePop: true
            })
            reject(new Error('关键词跳转转人工失败'))
          } else {
            this.goLaborLoading = true
            setTimeout(async () => {
              await THAT.goLabor({
                skipTelsalePop: true
              })
            }, 1000)
            reject(new Error('跳转转人工'))
          }
        }

        resolve(true)
      })
    },

    checkServiceTime() {  // 判断是否在转人工禁用时间
      const baseStore = useBase()
      const { noServiceBeginAt, noServiceEndAt } = baseStore
      const noServiceBeginAtArr = noServiceBeginAt.split(':').map(item => Number(item))
      const noServiceEndAtArr = noServiceEndAt.split(':').map(item => Number(item))

      let beginTime = dayjs().hour(noServiceBeginAtArr[0]).minute(noServiceBeginAtArr[1]).second(noServiceBeginAtArr[2])
      let endTime = dayjs().hour(noServiceEndAtArr[0]).minute(noServiceEndAtArr[1]).second(noServiceEndAtArr[2])

      if (beginTime.isAfter(endTime)) {  // 跨天
        endTime = endTime.add(1, 'day')
      }

      let afterBeginTime = beginTime.subtract(1, 'day')
      let afterEndTime = endTime.subtract(1, 'day')

      dayjs.extend(isBetween)
      const canGoLabor = dayjs().isBetween(beginTime, endTime) || dayjs().isBetween(afterBeginTime, afterEndTime)
      this.canGoLabor = !canGoLabor
      return canGoLabor
    },

    async checkServiceTimeHttp() {
      const res = await api.changeAgent()
      return res.access
    },

    setTimeCheckServiceTime() {
      setInterval(() => {
        this.checkServiceTime()
      }, 1000)
    },

    async goLabor({
      skipTelsalePop,
      skipSendlaborMsg,
    }: {
      skipTelsalePop?: boolean
      skipSendlaborMsg?: boolean
    } = {
      skipTelsalePop: false, // 是否跳过咨询类型弹窗判断
      skipSendlaborMsg: false, // 是否跳过发送转人工消息
    }) { // 转人工逻辑
      const baseStore = useBase()
      // if (!baseStore.initCompleted) throw Error('初始化尚未完成')

      this.goLaborLoading = true

      // 判断咨询类型是否显示
      if (!skipTelsalePop && useConfig().showMiddleJumptelsale) {
        this.showTelsalePop = true
        this.goLaborLoading = false
        return
      }

      await sendGoLaborPoint()

      const accessLabor = await this.checkServiceTimeHttp()
      if (!accessLabor) {
        ElMessage.error(baseStore.noServiceSentence)
        this.goLaborLoading = false
        return
      }

      if (!skipSendlaborMsg) {
        // 发送转人工消息
        await this.sendGoLaborMsg()
      } else {
        this.goLaborHandle()
      }
    },

    async goLaborHandle() {
      const search = location.search.slice(1)

      if (!this.isGoLaborBack) this.lastGolaborTime = new Date().getTime()

      if (isInNative() && getQueryObject().userId) {
        const connectionSearch = await this.getConnectionData()
        console.log('goZc', `${LaborUrl}&${search}&${connectionSearch}`)
        window.location.href = `${LaborUrl}&${search}&${connectionSearch}`
      } else {
        const connectionSearch = this.getH5ConnectionData()
        console.log('goZcH5', `${LaborUrl}&${search}&${connectionSearch}`)
        window.location.href = `${LaborUrl}&${search}&${connectionSearch}`
      }
    },

    async goRobot() { // 跳转智齿机器人  透传信息
      const search = location.search.slice(1)

      if (isInNative() && getQueryObject().userId) {
        const connectionSearch = await this.getConnectionData()
        console.log('goZcRobot', `${RobotUrl}&${search}&${connectionSearch}`)
        window.location.href = `${RobotUrl}&${search}&${connectionSearch}`
      } else {
        const connectionSearch = this.getH5ConnectionData()
        console.log('goZcRobotH5', `${RobotUrl}&${search}&${connectionSearch}`)
        window.location.href = `${RobotUrl}&${search}&${connectionSearch}`
      }
    },

    getH5ConnectionData() {
      const userStore = useUser()
      const customField4 = userStore.sessionId  // 会话ID
      const customField5 = userStore.channelName  // 渠道名
      const channelUniqueId = userStore.channelUniqueId  // 渠道id
      const channelId = userStore.channelId // 渠道uuid

      const paramsObj = {
        '对接会话ID': customField4,
        'IM渠道ID': channelId,
        'IM渠道UniqueId': channelUniqueId,
        'IM渠道名': customField5,
      }

      const targetSearch = `params=${JSON.stringify(paramsObj)}&customer_fields=${JSON.stringify({ customField4, customField5 })}`
      return targetSearch
    },

    async getConnectionData() { // 智齿数据打通
      const { appVersion, userId, phoneModel, fromPageName } = getQueryObject()
      const userStore = useUser()
      const userInfo:any = await browserGETMe()

      const res: any = await Promise.all([api.getSN(userInfo.onionId), api.getUserInfo(userId)])
      console.log('innn', res)
      const SN = res[0]
      const { currentBigVip, currentTrailBigVip } = res[1]
      console.log('innn', res[1])

      const tel = userInfo.phone  // 手机号
      const partnerid = userId // userID
      const uname = sanitizeUrlParam(userInfo.nickname) // 昵称
      const realIdentity = userInfo.realIdentity
      const onionId = userInfo.onionId
      const attribution = userInfo.attribution
      const customField4 = userStore.sessionId  // 会话ID
      const customField5 = userStore.channelName  // 渠道名
      const customField7 = SN.snIds.join('、')  // SN码
      const channelUniqueId = userStore.channelUniqueId  // 渠道id
      const channelId = userStore.channelId // 渠道uuid
      const customField8 = (currentBigVip || currentTrailBigVip) ? '大会员' : '非大会员'   // 会员身份
      const customField9 = res[1]?.remark ?? '' // 用户备注
      const customField10 = onionId // 洋葱id
      const customField11 = realIdentityTrans(realIdentity) // 身份

      const paramsObj = {
        '用户备注': customField9,
        'SN码': customField7,
        '会员身份': customField8,
        '手机号': tel,
        '来源页面': fromPageName || '',
        '用户ID': partnerid,
        '昵称': uname,
        '版本号': appVersion,
        '手机型号': phoneModel,
        '洋葱ID': onionId,
        '身份': realIdentityTrans(realIdentity),
        '用户归属': attribution,
        '对接会话ID': customField4,
        'IM渠道ID': channelId,
        'IM渠道UniqueId': channelUniqueId,
        'IM渠道名': customField5,
      }

      const targetSearch = `top_bar_flag=0&remark=${fromPageName}&tel=${tel}&partnerid=${partnerid}&uname=${uname}&params=${JSON.stringify(paramsObj)}&customer_fields=${JSON.stringify({ customField4, customField5, customField7, customField8, customField9, customField10, customField11 })}`

      return targetSearch

      function sanitizeUrlParam(str) {
        // 包括更多可能影响URL解析的特殊字符
        const illegalChars = /[ #?&=%/+;]/g
        return str.replace(illegalChars, '')
      }

      function realIdentityTrans(realIdentity) {
        switch (realIdentity) {
          case 'student':
            return '学生'
          case 'parents':
            return '家长'
          case 'student_parents':
            return '家长学生共用'
          default:
            return '未知'
        }
      }
    }
  },
  persist: {
    paths: ['lastGolaborTime']
  }
})
