import { defineStore } from 'pinia'
import api from '@/api'
import utils from '@constq/qzr-utils'
import { sendPageExposurePoint } from '@/script/point'
import { useLabor } from '@/store/labor'
import { useError } from '@/store/error'
import { useBase } from '@/store/base'
import { browserGETMe, getQueryObject, isInNative } from '@guanghe-pub/onion-utils'
import { useMenu } from './menu'


interface UserState {
  userType: 'customer' | 'kefu'

  accessKey: string
  deviceId: string
  instanceId: string
  topic: string
  groupId: string
  token: string
  uid: string

  sessionId: string

  channelId: string // 渠道id
  channelUniqueId: string // 渠道uuid
  channelName: string // 渠道名
  clickEnable: string // 传送门为空则使用原始默认配置，为true/false则使用后台配置的配置
  score: number // 延迟问题匹配度

  mqtt: any

  userId: string

  onionId: string
  gender: string  // 性别
  phone: string // 手机号
  uname: string // 昵称
  realIdentity: string  // 身份
  attribution: string // 用户归属

  fromPageName: string
  appVersion: string
  platform: string  // os
  phoneModel: string  // 手机型号

  fromPageNameError: boolean
}

export const useUser = defineStore({
  id: 'USER',
  state():UserState {
    return {
      userType: 'customer',

      accessKey: '',
      deviceId: '',
      instanceId: '',
      topic: '',
      groupId: '',
      token: '',
      uid: '',

      sessionId: '',

      channelId: '',
      channelUniqueId: '',
      channelName: '',
      clickEnable: '',
      score: 0,

      mqtt: null,
      userId: getQueryObject().userId,

      onionId: '',
      gender: '',
      phone: '',
      uname: '',
      realIdentity: '',
      attribution: '',

      fromPageName: '',
      appVersion: '',
      platform: '',
      phoneModel: '',

      fromPageNameError: false
    }
  },
  getters: {
    checkShowClickEnable(state) {
      if (state.clickEnable === '' || state.clickEnable === 'true') {
        return true
      } else {
        return false
      }
    },
    userParam(state: UserState) {
      const paramKey = [
        'userId', 'onionId', 'gender', 'phone', 'uname', 'realIdentity', 'attribution',
        'fromPageName', 'appVersion', 'platform', 'phoneModel'
      ]
      let target = ''
      for (const item of paramKey) {
        target += `${item}=${state[item]}&`
      }

      target += `from=${this.fromPageName}`
      return target
    },
  },
  actions: {
    setSessionId(sessionId: string) {
      this.sessionId = sessionId
      localStorage.setItem('IMSessionId', sessionId)
    },
    async getToken() {
      const res = await api.getToken({
        userType: this.userType
      })

      const { accessKey, deviceId, instanceId, token, uid, groupId, topic } = res
      this.accessKey = accessKey
      this.deviceId = deviceId
      this.instanceId = instanceId
      this.token = token
      this.groupId = groupId
      this.topic = topic
      if (!this.uid) this.uid = uid
    },
    async getChannelId() {
      const channel = utils.bom.param.getParam('channelId')
      console.log('渠道：', channel)
      useError().checkChannelEmpty(channel)

      await this.checkChannel(channel)
    },
    async getFromPageName() {
      // 端内启用校验
      console.log('是否在端内环境', isInNative())
      if (!isInNative()) return

      const { fromPageName } = getQueryObject()
      const target = fromPageName
      this.fromPageName = target
      console.log('fromPageName:', target)

      //  如果不带有from page name传参则不进行校验
      if (!target) return

      const res = await api.checkFromPageName(target)

      if (!res.pass) {
        this.fromPageNameError = true
        throw Error('fromPageName校验失败')
      }
    },
    async checkChannel(channel:string) {  // 判断当前渠道是否开放
      const res = await api.checkChannel(channel, {
        userId: this.userId
      })

      if (this.channelId !== res.channelId) { // 如果更改渠道重新获取sessionid
        this.setSessionId('')
      }

      this.channelId = res.channelId
      this.channelName = res.channelName
      this.channelUniqueId = res.channelUniqueId
      this.clickEnable = res.clickEnable
      this.score = res.score

      useBase().solution = res.solution // 欢迎语信息

      useLabor().setTransferLabor(res)

      console.log('渠道Id：', this.channelId)
      if (!res.access) {
        useLabor().goRobot()
        throw Error('渠道未开放')
      }

      useError().checkChannelEmpty(this.channelId)
      useMenu().getMenuList(res.channelUniqueId, this.userId)
      sendPageExposurePoint()
    },

    async reloadToken() {
      await this.getToken()
      this.mqtt.MQTTreloadToekn(this.token, this.deviceId)
    },

    async getAppInfo() {
      const userInfo:any = await browserGETMe()

      if (userInfo) {
        this.gender = userInfo.gender
        this.userId = userInfo.id
        this.phone = userInfo.phone
        this.uname = userInfo.uname
        this.realIdentity = userInfo.realIdentity
        this.onionId = userInfo.onionId
        this.attribution = userInfo.attribution
      }

      const { appVersion, platform, phoneModel, fromPageName } = getQueryObject()
      this.fromPageName = fromPageName
      this.appVersion = appVersion
      this.platform = platform
      this.phoneModel = phoneModel
    },

    splitPortal(url) {
      const target = url + `${url.includes('?') ? '&' : '?'}${this.userParam}`
      return target
    }
  },
  persist: {
    paths: ['uid', 'sessionId', 'channelId', 'channelName', 'channelUniqueId']
  }
})
