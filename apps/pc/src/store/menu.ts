import { defineStore } from 'pinia'
import api from '@/api'
import { Menu } from '@/api/modules/base'
import { useConfig } from './config'


interface MenuState {
  menuList: Menu[]
}

export const useMenu = defineStore({
  id: 'MENU',
  state():MenuState {
    return {
      menuList: [],
    }
  },
  getters: {
    menuListFilter(state): Menu[] {
      function filterMenusWithContentMiddle(menus: Menu[]): <PERSON>u[] {
        // 过滤器函数，根据条件递归筛选menu
        const filterCondition = (menu: Menu) => !menu.menuDescription.content.includes('middle/jumptelsale')

        // 递归处理menus数组
        const recursiveFilter = (menus: Menu[]): Menu[] => menus.reduce((acc: Menu[], menu: Menu) => {
          menu.menus = recursiveFilter(menu.menus)
          if (filterCondition(menu) || menu.menus.length > 0) {
            acc.push(menu)
          }
          return acc
        }, [])

        return recursiveFilter(menus)
      }

      if (useConfig().showMiddleJumptelsale) {
        return this.menuList
      } else {
        return filterMenusWithContentMiddle(this.menuList)
      }
    }
  },
  actions: {
    async getMenuList(channelUniqueId: string, userId: string) {
      const res = await api.getMenuApi(channelUniqueId, {
        userId: userId
      })
      this.menuList = res.menus ?? []
    }
  }
})
