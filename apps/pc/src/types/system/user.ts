export interface User {
  uid: number
  rid: number
  userName: string
  email: string
  groupId: number
  status: string
  imagePath: string
  phone: string
  isManager?: boolean
  isAdministrators?: boolean
}

export interface pwdUpdate {
  uid: number
  password: string
  passwordNew: string
}

export interface Guide {
  avatar: string // 头像
  nickName: string
  firstSentence: string
  expireSentence: string
  expireTime: number
  noServiceSentence: string
  relatedGuideSentence: string
  guideSentence: string
  id: number
  createdAt: string
  updatedAt: string
  deletedAt: string
}
