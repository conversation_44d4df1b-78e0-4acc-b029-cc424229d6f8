export interface SendMsgReq {
  channelId: string // 来源
  sender: string // 发送者id deviceId
  senderType: 'customer' | 'kefu' // 发送者类型 用户：customer 人工：kefu
  msg?: string // 消息
  msgType: 'img' | 'txt' | 'qanda' | 'timeout' | 'change' | 'close' // 消息类型 img:图片 txt:文本
  sysMsgType: 'system' | 'user' // 系统消息类型 系统:system
  receiver?: string // 接受者 deviceId
  receiverType: 'template' | 'openai' | 'kefu' | 'connect' | 'change' | 'close' // 接受者类型 常见问题模版：template 机器人：openai 人工：kefu
  time: number // 发送消息的时间
  sessionId?: string // 创建会话第一次不需要传该参数，
  receiverUid?: string  // 目前暂时不填
  senderUid: string // 当前用户uid
}

export interface MsgObj {
  me?: boolean // 是否是用户自己的消息
  msg?: string
  date?: string
  content?: any
  type?: 'similar' | 'txt' | 'qanda' | 'date' | 'revoke' | 'offline'  // 消息类型
  revoke?: boolean // 是否被撤回
  history?: boolean // 是否是历史消息
  time?: number
  sysMsg?: string  // 系统消息
  msgId?: string // 信息id
  origin?: any
}
