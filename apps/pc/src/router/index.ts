import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'PC',
    component: () => import('@/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('@/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/PC',
    name: 'PC',
    component: () => import('@/views/index.vue'),
    meta: {
      keepAlive: true
    }
  },
]

const router = createRouter({
  history: createWebHistory(''),
  routes,
})

router.beforeEach(beforeEach)

export default router
