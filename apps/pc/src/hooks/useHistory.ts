import api from '@/api'
import dayjs from 'dayjs'
import { useUser } from '@/store/user'
import { useBase } from '@/store/base'
import { useMessage } from '@/store/message'
import { convertStringWithLinks } from '@/script/msgProcessing'

export default function() {
  const pages = ref(1)
  const pageSize = 5
  const userStore = useUser()
  const baseStore = useBase()
  const msgStore = useMessage()

  const noHistory = ref(false)
  const historyLoading = ref(false)

  async function getHistory() {
    if (!baseStore.loadingCompleted) return

    historyLoading.value = true
    const res:any = await api.historyMsg({
      uid: userStore.uid,
      pages: pages.value,
      pageSize: pageSize,
      channelId: userStore.channelId,
      endTime: baseStore.inTime
    })

    pages.value += 1
    if (res.messages.length === 0) {
      noHistory.value = true
    } else {
      unshiftStore(res)
    }


    // console.log('历史消息列表：', msgStore.historyList)
    historyLoading.value = false
  }

  function format(msg) {
    const msgInfo = transJsonString(msg.msg)
    const target = typeof msgInfo === 'string' ? msg : { ...msg, ...msgInfo }

    let msgObj:any = {}
    if (msg.receiverType === 'connect') return
    else if (msg.msgType === 'timeout') return

    if (msg.msgType === 'txt') { msgObj = txtMsg(target) }
    else if (msg.msgType === 'qanda') { msgObj = qandaMsg(target) }
    else if (msg.msgType === 'similar') { msgObj = similarMsg(target) }
    else if (msg.msgType === 'similar_and_txt') { msgObj = similarAndTxt(target) }
    else { return msgObj }

    console.log(msgObj, msg)

    msgObj.history = true

    return msgObj
  }

  function unshiftStore(res) {
    const lastHistoryMsg = res.messages[res.messages.length - 1]
    const lastHistoryMsgTime = Number(lastHistoryMsg.time)
    let date = ''

    if (dayjs(lastHistoryMsgTime).isBefore(dayjs(), 'd')) {
      date = dayjs(lastHistoryMsgTime).format('YYYY年MM月DD日')
    } else {
      const lastSessionId = getSessionId(0)

      if (lastSessionId && (lastSessionId === lastHistoryMsg.sessionId)) {
        date = dayjs(lastHistoryMsgTime).format('HH:mm:ss')
      }
    }

    if (msgStore.offlineList.length === 0) {
      msgStore.historyList.unshift(...(res.messages.map(item => format(item))).filter(item => item).reverse())
    } else {
      // 离线消息插入历史消息
      const target:any[] = []
      const targetHistoryList = res.messages.map(item => format(item)).filter(item => item)
      targetHistoryList.forEach((msg, index) => {
        const lastMsg = index === 0 ? msgStore.historyList[msgStore.historyList.length - 1] : targetHistoryList[index - 1]
        msgStore.offlineList.reverse().forEach((offlineMsg) => {
          const msgTime = msg?.time ?? msg?.origin?.time
          const lastMsgTime = lastMsg?.time ?? lastMsg?.origin?.time
          if (dayjs(offlineMsg.createdAt).isBetween(checkTimeDayjs(msgTime), checkTimeDayjs(lastMsgTime))) {
            target.unshift({
              type: 'offline',
              msg: offlineMsg.content,
              msgType: offlineMsg.msgType
            })
          }
        })
        target.unshift(msg)
      })

      console.log(target)
      msgStore.historyList.unshift(...target)
    }

    function checkTimeDayjs(time) {
      if (!time) return dayjs()
      return String(time).length === 13 ? dayjs(Number(time)) : dayjs.unix(time)
    }

    if (date) {
      msgStore.historyList.unshift({
        type: 'date',
        date
      })
    }

    function getSessionId(index) {
      const target = msgStore.historyList[index]?.origin?.sessionId
      if (!target) {
        if (index >= 10) {
          return false
        } else {
          return getSessionId(index + 1)
        }
      } else {
        return target
      }
    }
  }

  // 文字消息处理
  function txtMsg(res) {
    const target:any = {
      msg: res.content,
      type: 'txt',
      date: calcTime(res.time),
      revoke: false,
      me: res.senderType === 'customer',
      origin: res,
    }

    if (res.msgStatus === 'revoke') {
      target.revoke = true
      target.type = 'revoke'
    }

    if (res.sysMsgType === 'user') {
      if (res.receiverType === 'template') {  // 用户点击问题卡片链接
        target.msg = res.qandaQuestion
      } else if (res.receiverType === 'openai') { // 用户自行发送的消息
        target.msg = res.msg
      }
    }

    return target
  }

  // 卡片式交互问题
  function qandaMsg(res) {
    res.content.answer = convertStringWithLinks(res.content.answer)

    return ({
      type: 'qanda',
      date: calcTime(res.time),
      me: false,
      revoke: false,
      content: res.content,
      origin: res
    })
  }

  function similarMsg(res) {
    return ({
      type: 'similar',
      date: calcTime(res.time),
      me: false,
      revoke: false,
      ...res
    })
  }

  function similarAndTxt(res) {
    let msg = convertStringWithLinks(res.content.answer)

    const target =  {
      type: 'similarAndTxt',
      date: calcTime(res.time),
      me: false,
      revoke: false,
      ...res,
      msg,
      content: res.content.similar
    }

    if (res.msgStatus === 'revoke') {
      target.revoke = true
      target.type = 'revoke'
    }

    return target
  }

  return { getHistory, historyLoading, noHistory }
}

function transJsonString(str) {
  try {
    const toObj = JSON.parse(str) // json字符串转对象
    if (toObj && typeof toObj === 'object') {
      return toObj
    } else {
      return str
    }
  } catch {}
  return str
}

function calcTime(time) {
  if (String(time).length === 10) {
    return dayjs.unix(Number(time)).format('HH:mm:ss')
  } else {
    return dayjs(Number(time)).format('HH:mm:ss')
  }
}
