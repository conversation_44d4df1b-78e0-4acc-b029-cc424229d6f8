import dayjs from 'dayjs'
import { useBase } from '@/store/base'
import { useUser } from '@/store/user'
import { useMessage } from '@/store/message'
import { convertStringWithLinks } from '@/script/msgProcessing'
import { useLabor } from '@/store/labor'

export default function({ scrollBottom }) {
  const baseStore = useBase()
  const userStore = useUser()
  const msgStore = useMessage()
  const laborStore = useLabor()

  const finishList:string[] = []

  function getMsg(val) {
    const res = JSON.parse(val.payloadString)
    console.log('res', res)
    const { msgType, finish } = res

    if (res.msgType === 'change') {  // 正常转人工
      laborStore.goLaborHandle()
      return
    }

    if (res.msgType === 'queuing') {  // 排队拦截转人工
      laborStore.goLaborLoading = false
    }

    if (checkSimilarAndTxt(msgType) || msgType === 'qanda' || msgType === 'similar') {
      baseStore.waitMsgLoading = false

      if (finish === true) {
        finishList.push(res.msgId)
        baseStore.streamLoading = false
        msgStore.labelLoading = false

        if (checkSimilarAndTxt(msgType)) {
          if (res.content.maxScore >= userStore.score) {
            useLabor().delayCount += 1
          }
        }

      } else if (finish === false) {
        if (!finishList.includes(res.msgId)) {
          baseStore.streamLoading = true
        }
      }
    }

    // 过滤IM端部展示的相关问题
    if (checkSimilarAndTxt(msgType)) {
      res.content.similar = res.content.similar.filter(item => !item.shield)
    } else if (msgType === 'similar') {
      res.content = res.content.filter(item => !item.shield)
    }

    msgStore.setIDRecoveryMsgList(res)  // 更新最后一条消息的快速回答列表

    if (msgType === 'txt' || msgType === 'queuing') txtMsg(res)
    else if (msgType === 'qanda') qandaMsg(res)
    else if (msgType === 'timeout') timeoutMsg(res)
    else if (msgType === 'revoke') revokeMsg(res)
    else if (msgType === 'similar') similarMsg(res)
    else if (msgType === 'renew') renewMsg(res)
    else if (checkSimilarAndTxt(msgType)) similarAndTxt(res)

    baseStore.loading = false
    scrollBottom()
  }

  // 文字消息处理
  function txtMsg(res) {
    let msg = convertStringWithLinks(res.content)

    msgStore.list.push({
      msg: msg,
      // msg: convertMarkdownLinksToHTML(res.content),
      type: 'txt',
      date: dayjs.unix(res.time).format('HH:mm:ss'),
      me: false,
      revoke: false,
      ...res
    })

  }

  // 卡片式交互问题
  function qandaMsg(res) {
    res.content.answer = convertStringWithLinks(res.content.answer, true)

    msgStore.list.push({
      type: 'qanda',
      date: dayjs.unix(res.time).format('HH:mm:ss'),
      me: false,
      revoke: false,
      ...res
    })
  }

  function similarMsg(res) {
    msgStore.list.push({
      type: 'similar',
      date: dayjs.unix(res.time).format('HH:mm:ss'),
      me: false,
      revoke: false,
      ...res
    })
  }

  function similarAndTxt(res) {
    let msg = convertStringWithLinks(res.content.answer)

    const findOne = checkMsgList(res)
    if (!findOne) {
      msgStore.list.push({
        type: 'similarAndTxt',
        date: dayjs.unix(res.time).format('HH:mm:ss'),
        me: false,
        revoke: false,
        msg,
        ...res,
        content: res.content.similar,
        origin: res
      })
    }

    function checkMsgList(res) {
      const findOne = msgStore.list.find(item => {
        return item.msgId === res.msgId
      })

      if (findOne) {
        findOne.msg = msg
        return true
      }

      return false
    }
  }

  // 撤回信息处理
  function revokeMsg(res) {
    const target = msgStore.list.find(item => {
      return res.content === item.msgId
    })
    target!.revoke = true
    target!.type = 'revoke'
    target!.me = false
  }

  // 超时处理
  function timeoutMsg(res) {
    baseStore.timeout = true
    userStore.setSessionId('')
  }

  // 重复开启页面
  function renewMsg(res) {
    baseStore.renew = true
    baseStore.renewStr = res.content
  }

  return {
    getMsg
  }
}

function checkSimilarAndTxt(type: string) {
  if (type === 'similar_and_txt' || type === 'es' || type === 'vector') return true
  return false
}
