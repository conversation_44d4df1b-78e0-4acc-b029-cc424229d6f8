import dayjs from 'dayjs'
import { SendMsgReq } from '@/types/mssage'
import { useUser } from '@/store/user'
import api from '@/api'
import { useBase } from '@/store/base'
import { useLabor } from '@/store/labor'
import { useMessage } from '@/store/message'

export default function({ textarea, scrollBottom }) {
  const userStore = useUser()
  const baseStore = useBase()
  const msgStore = useMessage()
  const laborStore = useLabor()

  // 发送ai信息
  async function sendAiMsg() {
    if (!baseStore.initCompleted) throw Error('初始化尚未完成')
    if (!textarea.value) throw Error('输入栏为空')
    if (baseStore.timeout) throw Error('超时')
    if (baseStore.loading || baseStore.streamLoading) throw Error('等待上一条消息返回')
    if (testEmpty(textarea.value) === '') throw Error('仅包含空字符')

    await useLabor().checkMessagePointGoLabor(textarea.value) // 判断是否匹配转人工point

    const checkIDRecoverMsg = msgStore.checkIDRecoverMsg(textarea.value)
    if (checkIDRecoverMsg) {
      sendTmpMsg(checkIDRecoverMsg.id, checkIDRecoverMsg.content)
      textarea.value = ''
      return
    }

    baseStore.loading = true
    baseStore.waitMsgLoading = true

    msgStore.list.push({
      msg: textarea.value,
      type: 'txt',
      date: dayjs(new Date()).format('HH:mm:ss'),
      time: new Date().getTime(),
      me: true
    })

    const params:SendMsgReq = {
      ...baseMsgParam(),
      msg: textarea.value,
      senderType: 'customer',
      msgType: 'txt',
      sysMsgType: 'user',
      receiverType: 'openai',
    }

    textarea.value = ''
    await sendMsgHandle(params)
    // useLabor().delayCount += 1

    // 滚动到底部
    scrollBottom()

    // 聚焦输入栏
    // msgTextareaRef.value.focus()
  }

  // 发送模板信息
  async function sendTmpMsg(id, msg) {
    if (!baseStore.initCompleted) throw Error('初始化尚未完成')
    if (baseStore.loading || baseStore.streamLoading) return
    if (baseStore.timeout) return

    baseStore.loading = true

    msgStore.list.push({
      msg: msg,
      type: 'txt',
      date: dayjs(new Date()).format('HH:mm:ss'),
      time: new Date().getTime(),
      me: true
    })

    const params:SendMsgReq = {
      ...baseMsgParam(),
      msg: JSON.stringify({
        qandaUniqueId: id,
        qandaQuestion: msg
      }),
      senderType: 'customer',
      msgType: 'txt',
      sysMsgType: 'user',
      receiverType: 'template',
    }

    await sendMsgHandle(params)

    scrollBottom()
  }

  // 返回发送信息参数重通用的部分
  function baseMsgParam() {
    return {
      channelId: userStore.channelId,
      time: new Date().getTime(),
      sender: userStore.deviceId,
      sessionId: userStore.sessionId,
      senderUid: userStore.uid,
      receiver: '',
    }
  }

  // 调用发送信息接口
  async function sendMsgHandle(params) {
    if (params.sessionId === '') return
    const res = await api.sendMsg(params)
  }

  // 回车/shift回车
  function msgEvent(e) {
    e.stopPropagation()

    if (e.key === 'Enter' && e.shiftKey) {
      e.preventDefault()
      textarea.value += '\n'  // 回车加换行符
      return
    }

    if (e.key === 'Enter') {
      e.preventDefault()
      sendAiMsg()
      return
    }
  }

  // 发送转人工信息
  async function sendGoLaborMsg() {
    const params:SendMsgReq = {
      ...baseMsgParam(),
      msgType: 'change',
      sysMsgType: 'system',
      senderType: 'customer',
      receiverType: 'change',
    }
    await sendMsgHandle(params)
  }

  laborStore.sendGoLaborMsg = sendGoLaborMsg

  // 发送主动断开连接信息
  async function sendCloseMsg() {
    const params:SendMsgReq = {
      ...baseMsgParam(),
      msgType: 'close',
      sysMsgType: 'system',
      senderType: 'customer',
      receiverType: 'close',
    }
    await sendMsgHandle(params)
  }

  return {
    sendAiMsg, sendTmpMsg, msgEvent, sendGoLaborMsg, sendCloseMsg
  }
}

// 测试是否仅包含回车符、空格符
function testEmpty(str:string) {
  return str.replace(/(^\s*)|(\s*$)/g, '')
}
