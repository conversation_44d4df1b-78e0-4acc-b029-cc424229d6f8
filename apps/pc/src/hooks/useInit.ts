import { useUser } from '@/store/user'
import { useLabor } from '@/store/labor'
import dayjs from 'dayjs'
import { useConfig } from '@/store/config'
import { useBase } from '@/store/base'
import { useMessage } from '@/store/message'

export default function({ setMqtt, setHeart, getConnect, scrollBottom }) {
  const userStore = useUser()

  onMounted(async () => {
    if (dayjs().subtract(3, 'minute').isBefore(useLabor().lastGolaborTime)) { // 如果三分钟内转过人工，直接跳转
      useLabor().isGoLaborBack = true
      useLabor().goLabor({
        skipTelsalePop: true,
        skipSendlaborMsg: true,
      })
    } else {
      useUser().getAppInfo()
      useConfig().getPortal()

      await userStore.getFromPageName()
      await userStore.getToken()
      await userStore.getChannelId()

      await getConnect()
      await setMqtt()

      useMessage().getOffline()

      // 初始化完成
      useBase().initCompleted = true

      setHeart()

      scrollBottom()
    }
  })
}
