import { useUser } from '@/store/user'
import { useBase } from '@/store/base'
import pinia from '@/store'
import api from '@/api'
import MQTT from '@/script/mqtt'
import { useLabor } from '@/store/labor'

export default function(getMsg) {
  const userStore = useUser(pinia)
  const baseStore = useBase()

  const heartInterval = ref()
  let Mqtt

  function setHeart() { // 部署心跳
    api.heartbeat({
      deviceId: userStore.deviceId,
      type: userStore.userType
    })

    heartInterval.value = setInterval(() => {
      api.heartbeat({
        deviceId: userStore.deviceId,
        type: userStore.userType
      })
    }, 3 * 60 * 1000)
  }

  async function setMqtt() {  // 实例化mqtt
    const mqtt = new MQTT({
      accessKey: userStore.accessKey,
      instanceId: userStore.instanceId,
      deviceId: userStore.deviceId,
      token: userStore.token,
    }, getMsg, lostMqttCb)

    await mqtt.MQTTconnect()
    userStore.mqtt = mqtt
    Mqtt = mqtt
  }

  async function getConnect() { // 获取sessionId 获取基本信息 创建会话
    const par:any = {
      sender: userStore.deviceId,
      senderType: 'customer',
      senderUid: userStore.uid,
      channelId: Number(userStore.channelId),
    }
    const sessionId = localStorage.getItem('IMSessionId')
    if (sessionId) par.sessionId = sessionId

    const params = new URLSearchParams(location.search)
    par.onionUserId = params.get('userId')
    const res = await api.connect(par)
    if (res.callReuse) {  // 三分钟内转人工结束对话 直接跳智齿人工
      useLabor().goLabor({
        skipTelsalePop: true
      })
      return
    }

    userStore.setSessionId(res.sessionId)
    baseStore.setRobotBaseInfo(res.guide)
    baseStore.loadingCompleted = true

    if (baseStore.timeout) baseStore.timeout = false
    if (baseStore.renew) baseStore.renew = false
    if (baseStore.networkTimeout) baseStore.networkTimeout = false
    if (baseStore.connectLoading) baseStore.connectLoading = false
  }

  async function reconnect() {  // 重连判断
    if (baseStore.connectLoading) return

    baseStore.connectLoading = true
    const isConnected = userStore.mqtt.mqtt.isConnected() // 判断mqtt是否断开连接

    if (isConnected) {
      await getConnect()  // mqtt已连接 重连会话
    } else {
      await lostMqttCb()  // mqtt断开 重连mqtt
    }
    baseStore.connectLoading = false
  }

  async function lostMqttCb() { // mqtt断开重连
    await userStore.reloadToken() // 重新获取token等mqtt配置
    await Mqtt.MQTTconnect()  // mqtt重连
    await getConnect() // 创建会话
  }

  onUnmounted(() => {
    clearInterval(heartInterval.value)
  })

  return { setHeart, getConnect, setMqtt, reconnect }
}
