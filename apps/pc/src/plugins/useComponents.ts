/*
 * @Date         : 2021-09-02 15:08:52
 * @Description  : 全局引用components common组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2023-08-01 17:07:06
 */


export default function(Vue:any) {
  const files:any = import.meta.glob('../components/common/*.vue', { eager: true })


  Object.keys(files).forEach((fileName:string) => {
    let componentname:any = fileName.replace(/(\.\/|\.vue)/g, '').split('/')
    componentname = componentname[componentname.length - 1]
    Vue.component('common-' + componentname, files[fileName].default)
  })
}
