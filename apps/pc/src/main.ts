import { createApp } from 'vue'
import App from './App.vue'

import router from './router/index'
import pinia from '@/store'

import Plugins from '@/plugins'
import '@guanghe-pub/onion-utils'

import 'virtual:svg-icons-register'
import 'virtual:uno.css'
import '@unocss/reset/normalize.css'

// import '@vant/touch-emulator'

const app = createApp(App)

app.use(Plugins)
app.use(router)
app.use(pinia)
app.mount('#app')
