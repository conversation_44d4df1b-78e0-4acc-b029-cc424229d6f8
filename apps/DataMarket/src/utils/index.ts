/**
 * @Date         : 2023-06-17 16:21:19
 * @Description  : 工具函数
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

/**
 * @description: 金额千分格式化
 * @param {*} num
 * @return {*}
 */
export function formatAmount(number) {
  if (!number) return number
  let numStr = number.toString() // 将数字转换为字符串
  let parts = numStr.split('.') // 分割整数部分和小数部分
  let integerPart = parts[0] // 整数部分

  let formattedNumber = ''
  let count = 0

  // 从整数部分的最后一位开始遍历
  for (let i = integerPart.length - 1; i >= 0; i--) {
    formattedNumber = integerPart[i] + formattedNumber // 将当前位添加到结果字符串的前面
    count++

    // 每隔三位加一个逗号，除非是最后一位或者当前位是负号
    if (count % 3 === 0 && i !== 0 && integerPart[i - 1] !== '-') {
      formattedNumber = ',' + formattedNumber
    }
  }

  // 如果有小数部分，将其添加到结果字符串末尾
  if (parts.length > 1) {
    formattedNumber += '.' + parts[1]
  }

  return formattedNumber
}

/**
 * @description: 超出五个字符截取前五
 * @param {*} str
 * @return {*}
 */
export function sliceStr(str, length = 5) {
  if (!str) return str

  let target = str
  if (str.length > 5) target = str.slice(0, length) + '...'

  return target
}


/**
 * @description: 动态获取静态资源地址
 * @param {*} url
 */
export function getAssetsImage(url: string) {
  return new URL(`../assets/${url}`, import.meta.url).href
}

// 获取url中的参数
export function getUrlParam(name) {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)') // 构造一个含有目标参数的正则表达式对象
  let r = window.location.search.substr(1).match(reg)  // 匹配目标参数
  if (r != null) return decodeURI(r[2]); return null // 返回参数值
}
