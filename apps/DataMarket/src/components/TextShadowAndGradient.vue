<!--
 * @Date         : 2024-06-19 18:43:52
 * @Description  : 字体阴影+渐变色
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="text"
       :data-content="text"
       :style="style">{{ text }}</div>
</template>

<script lang='ts' setup>
const props = withDefaults(defineProps<{
  text: string | number
  style?: any
}>(), {
  text: ' ',
  style: {}
})
</script>

<style scoped lang='scss'>
.text { 
  font-weight: 600;
  background: linear-gradient(0deg, #FFEEB5 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
