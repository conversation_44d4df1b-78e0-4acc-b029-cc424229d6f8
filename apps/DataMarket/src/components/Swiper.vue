<!--
 * @Date         : 2024-06-20 14:14:09
 * @Description  : 轮播组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="swiper-container">
    <van-swipe vertical
               :autoplay="autoplay"
               :show-indicators="false">
      <van-swipe-item v-for="(ite, idx) in rankListHandle"
                      :key="idx">
        <div v-for="(item, index) of ite"
             :key="index">
          <div v-if="!item[0]"
               class="rank-item-none" />
          <div v-else
               class="rank-item"
               :class="{isTop: checkTop(idx, index)}">
            <div v-if="checkTop(idx, index)">
              <Medal :rank="index + 1" />
            </div>
            <div v-else>NO.{{ NoCalc(idx, index) }}</div>
            <div>{{ item[0] }}</div>
            <div>{{ item[1] }}</div>
            <div>{{ item[2] }}</div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script lang='ts' setup>
const props = withDefaults(defineProps<{
  showNumber: number // 显示的数量
  dataList: any[]
  autoplay: number
  personRank?: boolean  // 是否为个人当配
}>(), {
  personRank: false
})

const rankListHandle = computed(() => {
  const target = props.dataList?.reduce((prev, curr, index) => {
    const i = Math.floor(index / props.showNumber)
    prev[i] = [...prev[i] || [], ...[curr]]
    return prev
  }, [])
  return target
})

function checkTop(idx, index) {
  const isTop = (index === 0 || index === 1 || index === 2)
  return props.personRank ? isTop : (idx === 0 && isTop)
}

function NoCalc(idx, index) {
  return props.personRank ? (index + 1) : (idx * props.showNumber) + index + 1
}
</script>

<style scoped lang='scss'>
.swiper-container {
  :deep(.van-swipe__track) {
    width: 472px;
  }
}

.rank-item {
  display: flex;
  position: relative;
  align-items: center;
  margin-bottom: -2.2px;
  font-size: 20px;
  color: white;
  width: 100%;

  >div {
    text-align: center;
    position: absolute;
  }

  >div:nth-child(1) {
    left: 10px;
    width: 60px;
    display: flex;
    justify-content: center;
  }

  >div:nth-child(2) {
    left: 100px;
    width: 100px;
  }

  >div:nth-child(3) {
    width: 120px;
    left: 215px;
  }

  >div:nth-child(4) {
    left: 340px;
    width: 120px;
  }
}

.rank-item-none {
  height: 46px;
  width: 472px;
}

.rank-item {
  background: url('@/assets/line_bg.png') no-repeat;
  background-size: 100% 100%;
  height: 46px;
  width: 472px;
}

.isTop {
  color: #FFEE9F;
}
</style>
