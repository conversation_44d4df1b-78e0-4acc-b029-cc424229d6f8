<!--
 * @Date         : 2024-06-20 18:15:31
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <video v-if="!double"
           height="100vh"
           width="100vw"
           src="@/assets/firework/firework.webm"
           loop
           muted
           autoplay
           class="video" />
    <video v-if="double"
           height="100vh"
           width="100vw"
           src="@/assets/firework/firework1.webm"
           loop
           muted
           autoplay
           class="video" />
  </div>
</template>

<script lang='ts' setup>
const props = defineProps<{
  double: boolean
}>()
</script>

<style scoped lang='scss'>
.video {
  width: 100vw;
  height: 100vh;
  z-index: 101;
  position: fixed;
  top: 0;
  left: 0;
  object-fit: fill;
}
</style>
