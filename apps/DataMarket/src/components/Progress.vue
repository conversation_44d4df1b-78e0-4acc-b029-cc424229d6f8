<!--
 * @Date         : 2024-06-20 11:13:58
 * @Description  : 排行榜进度条
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flex items-center justify-start">
    <div class="progress-box mr-5px">
      <div class="progress"
           :class="{progress100: progress > 100, less10: progress <= 10}" />
    </div>

    <div class="font-600 text-20px c-white w-90px">{{ props.progress.toFixed(2) }}%</div>
  </div>
</template>

<script lang='ts' setup>

const props = withDefaults(defineProps<{
  progress: number
  isTop: boolean
}>(), {
  progress: 0,
  isTop: false
})

const progressW = computed(() => {
  return (props.progress > 100 ? 100 : props.progress).toFixed(2) + '%'
})

const progressColor = computed(() => {
  return props.isTop ? '#F3CD1F' : '#7C5DF0'
})
</script>

<style scoped lang='scss'>
.progress-box {
  background: white;
  padding: 2px;
  border-radius: 50px;
  width: 70px;
  height: 14px;
}
.progress {
  background: v-bind(progressColor);
  border-radius: 50px 0 0 50px;
  height: 100%;
  width: v-bind(progressW);
}

.progress100 {
  border-radius: 50px !important;
}

.less10 {
  margin-left: 2px;
}
</style>
