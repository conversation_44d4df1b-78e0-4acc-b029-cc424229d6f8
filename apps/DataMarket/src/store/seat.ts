import { defineStore } from 'pinia'
import api from '@/api'
import { useGlobal } from './global'

interface SeatState {
  monthPersonRankList: any[]
  weekRankList: any[]
  newbieList: any[] // 新人黑马排行榜
  todayPersonTopInfo: any
  weekPersonTopInfo: any
  targetInfo: any
}

export const useSeat = defineStore({
  id: 'Seat',
  state(): SeatState {
    return {
      monthPersonRankList: [],
      weekRankList: [],
      newbieList: [],
      todayPersonTopInfo: {
        name: '邱卓然',
        score: 99999
      },
      weekPersonTopInfo: {
        name: '邱卓然',
        score: 99999
      },
      targetInfo: {
        goal: 80000000,
        actual: 0,
        completionRate: 0
      }
    }
  },
  getters: {
    completionOver100(): boolean {
      return Number(this.targetInfo.completionRate) >= 100
    },
    weekRankListFormat(state): any[] {
      if (useGlobal().city === '长沙') {
        return this.weekRankList
      }
      const target: any = []
      const groupList = ['星火军团', '无畏军团', '业绩最红团', '平板军团', '第一军团', '勇者军团', '天下第一团', '武汉新兵营']

      groupList.forEach(group => {
        let count = 0
        this.weekRankList.forEach(item => {
          if (item.team === group && count < 5) {
            target.push(item)
            count++
          }
        })

        if (count !== 0) {
          const empty = 5 - count
          for (let i = 0; i < empty; i++) {
            target.push({
              team: group,
            })
          }
        }
      })

      return target
    }
  },
  actions: {
    async getSeatData() { // 获取坐席数据
      const data = await api.getSeatList()

      this.monthPersonRankList = data.revenue.monthRevenues.people
      this.todayPersonTopInfo = data.revenue.dayChampion.people
      this.newbieList = data.newbie?.people
      this.weekPersonTopInfo = data.revenue.weekChampion.people
      this.weekRankList = data.convertAmount.week.people
      this.targetInfo = {
        goal: data.goal,
        actual: data.actual,
        completionRate: data.completionRate
      }
    }
  },
})
