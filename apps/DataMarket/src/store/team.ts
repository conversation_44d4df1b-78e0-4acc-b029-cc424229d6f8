import { defineStore } from 'pinia'
import api from '@/api'

interface TeamState {
  monthTeamRankList: any[]
  monthRankList: any[]
  teamRankList: any[]
  todayTeamTopInfo: any
  weekTeamTopInfo: any
}

export const useTeam = defineStore({
  id: 'TEAM',
  state(): TeamState {
    return {
      monthTeamRankList: [],
      monthRankList: [],
      teamRankList: [],
      todayTeamTopInfo: {
        group: '业绩最红',
        team: '业绩最红',
        amount: 999999
      },
      weekTeamTopInfo: {
        group: '业绩最红',
        team: '业绩最红',
        amount: 999999
      },
    }
  },
  getters: {
  },
  actions: {
    async getTeamData() { // 获取小组数据
      const data = await api.getTeamList()

      this.monthTeamRankList = data.revenue.groupRevenues.group
      this.monthRankList = data.convertAmount.monthConvertAmount.group
      this.teamRankList = data.revenue.teamRevenues.group
      this.todayTeamTopInfo = data.revenue.dayChampion.group
      this.weekTeamTopInfo = data.revenue.weekChampion.group
    }
  },
  persist: {
    paths: ['userId']
  }
})
