import { defineStore } from 'pinia'
import { getUrlParam } from '@/utils'
import { useSeat } from './seat'
import { useTeam } from './team'
import api from '@/api'

interface GlobalState {
  city: '武汉' | '长沙'
  currentPage: 'person' | 'team'
}

export const useGlobal = defineStore({
  id: 'GLOBAL',
  state(): GlobalState {
    return {
      city: '武汉',
      currentPage: 'person'
    }
  },
  getters: {
    cityIsWH(): boolean {
      return this.city === '武汉'
    },
    cityCode(): string {
      return this.city === '武汉' ? 'wuhan' : 'changsha'
    }
  },
  actions: {
    changeCurrentPage() {
      if (this.currentPage === 'person') {
        this.currentPage = 'team'
      } else {
        this.currentPage = 'person'
      }
    },
    getCity() {
      const city = getUrlParam('city')
      if (city === 'wh') {
        this.city = '武汉'
      } else {
        this.city = '长沙'
      }
    },
    changeCity() {
      if (this.cityIsWH) {
        this.city = '长沙'
      } else {
        this.city = '武汉'
      }

      useTeam().getTeamData()
      useSeat().getSeatData()
    }
  },
})
