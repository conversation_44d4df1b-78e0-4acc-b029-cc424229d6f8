.person-top {
  width: 100%;
  margin-bottom: 21PX;

  .title-box {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 32PX;
  }

  .content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 53PX 18PX 10PX 18PX;
    background: url('@/assets/bg-content.png') no-repeat;
    background-size: 100% 100%;
    >img {
      margin-right: 25PX;
    }
    > div {
      > img {
        margin-bottom: 10PX;
      }
    }
  }

  .info {
    color: #19ecff;
    font-size: 52PX;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    height: 100%;
    width: 210PX;
  }

  .income {
    color: #19ecff;
    font-size: 58PX;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    flex-direction: column;
  }

  .info > div:last-child {
    margin-top: 10PX;
    font-size: 24PX;
  }
}

.team-top {
  .info {
    > div {
      margin-bottom: 10PX;
      font-size: 40PX;
    }
  }
  .income {
    > div {
      font-size: 50PX;
    }
  }
}
