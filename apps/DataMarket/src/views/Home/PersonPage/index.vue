<!--
 * @Date         : 2024-06-19 14:03:09
 * @Description  : 个人记录页
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="content-container relative">
    <FireWorkVideo v-if="seatStore.completionOver100"
                   :double="true" />

    <div class="z-100 relative">
      <Top class="relative! top-117px left-60px" />
      <MonthPersonRank class="absolute! top-396px left-58px" />

      <Target class="absolute! top-85px left-583px" />
      <Progress />

      <div class="absolute! top-716px left-577px">
        <div class="flex">
          <TodayPersonTop class="mr-8px" />
          <WeekPersonTop />
        </div>
      </div>

      <WeekPersonRank class="absolute! top-117px right-57px" />
      <BlackHorseRank class="absolute! top-479px right-57px" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useSeat } from '@/store/seat'
import Top from './children/Top.vue'
import Target from './children/Target.vue'
import Progress from './children/Progress.vue'
import TodayPersonTop from './children/TodayPersonTop.vue'
import WeekPersonTop from './children/WeekPersonTop.vue'
import MonthPersonRank from './children/MonthPersonRank.vue'
import WeekPersonRank from './children/WeekPersonRank.vue'
import BlackHorseRank from './children/BlackHorseRank.vue'
import FireWorkVideo from '@/components/FireWorkVideo.vue'

const seatStore = useSeat()

onMounted(() => {
  seatStore.getSeatData()
})
</script>

<style scoped lang='scss'>
.content-container {
  overflow: hidden;
  height: 1010px;
  width: 1920px;
  background-image: url('@/assets/bg.png');
  background-size: 100% 100%;
}
</style>
