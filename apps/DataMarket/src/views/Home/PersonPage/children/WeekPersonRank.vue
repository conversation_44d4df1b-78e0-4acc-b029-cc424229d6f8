<!--
 * @Date         : 2023-06-12 17:19:19
 * @Description  : 个人当配转化排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="week-person-rank-container relative">
    <div class="top-93px left-12px absolute overflow-hidden h-220px">
      <Swipe :data-list="rankList"
             :width="462"
             :autoplay="globalStore.cityIsWH ? 15000 : 0"
             person-rank
             :show-number="5" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { sliceStr, formatAmount } from '@/utils'
import Swipe from '@/components/Swiper.vue'
import { useSeat } from '@/store/seat'
import { useGlobal } from '@/store/global'

const globalStore = useGlobal()
const seatStore = useSeat()

const rankList = computed(() => {
  const target = seatStore.weekRankListFormat.map(item => {
    if (!item?.name) return ['', '', '']
    return [sliceStr(item.name), sliceStr(item.team), formatAmount(item.score)]
  })
  return target
})
</script>

<style scoped lang='scss'>
.week-person-rank-container {
  width: 496px;
  height: 332px;
  background: url('@/assets/model/week_person_rank.png') no-repeat;
  background-size: 100% 100%;
}
</style>
