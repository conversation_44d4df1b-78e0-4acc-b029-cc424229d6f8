<!--
 * @Date         : 2024-06-20 15:01:40
 * @Description  : 新人黑马排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="black-horse-rank-container relative">
    <div class="top-94px left-12px absolute overflow-hidden h-350px">
      <Swipe :data-list="rankList"
             :width="462"
             :autoplay="globalStore.cityIsWH ? 20000 : 0"
             :show-number="8" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { sliceStr, formatAmount } from '@/utils'
import Swipe from '@/components/Swiper.vue'
import { useSeat } from '@/store/seat'
import { useGlobal } from '@/store/global'

const globalStore = useGlobal()
const seatStore = useSeat()
const rankList = computed(() => {
  const target = seatStore.newbieList?.map(item => {
    return [sliceStr(item.name), formatAmount(item.score), sliceStr(item.group)]
  })
  return target ?? []
})
</script>

<style scoped lang='scss'>
.black-horse-rank-container {
  width: 496px;
  height: 470px;
  background: url('@/assets/model/black_horse_rank.png') no-repeat;
  background-size: 100% 100%;
}
</style>
