<!--
 * @Date         : 2023-06-12 17:01:48
 * @Description  : 今日个人业绩冠军
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="today-top-container relative">
    <TextShadowAndGradient :text="sliceStr(seatStore?.todayPersonTopInfo?.name, 3)"
                           class="absolute! text-44px font-bold top-68px left-170px" />
    <TextShadowAndGradient :text="formatAmount(seatStore?.todayPersonTopInfo?.score)"
                           class="absolute! text-38px font-bold italic top-158px left-185px" />
  </div>
</template>

<script setup lang="ts">
import { useSeat } from '@/store/seat'
import { sliceStr, formatAmount } from '@/utils'
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'

const seatStore = useSeat()
</script>

<style scoped lang='scss'>
.today-top-container {
  background: url('@/assets/model/today_person_top.png') no-repeat;
  background-size: cover;
  width: 379px;
  height: 234px;
}
</style>
