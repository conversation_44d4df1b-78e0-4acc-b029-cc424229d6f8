<!--
 * @Date         : 2023-06-12 15:36:49
 * @Description  : 总目标的金额和进度条
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="target-container relative">
    <div class="absolute top-150px left-114px target-amount">
      {{ amountStr }}
    </div>
    <div class="absolute top-150px left-418px target-amount">
      {{ amountActualStr }}
    </div>
  </div>
</template>

<script setup lang='ts'>
import { useSeat } from '@/store/seat'

const amountStr = computed(() => {
  return String(useSeat().targetInfo.goal).split('').slice(0, 4).join('') + '万'
})

const amountActualStr = computed(() => {
  return String(useSeat().targetInfo.actual).split('').slice(0, 4).join('') + '万'
})
</script>

<style scoped lang='scss'>
.target-container {
  width: 754px;
  height: 234px;
  background: url('@/assets/model/mubiao.png') no-repeat;
  background-size: 100% 100%;
}

.target-amount {
  font-weight: bold;
  font-size: 60px;
  background: linear-gradient(0deg, #B7000A 0.4150390625%, #FF4200 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
