<!--
 * @Date         : 2024-07-19 16:27:48
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2024-07-19 16:27:48
 * @Description  : 进度条组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="progress-container">
    <div class="progress-bar">
      <div class="progress"
           :style="{ width: progressWidth }" />
      <div class="relative w-full top--24px">
        <div v-for="(item, index) in progressPoints"
             :key="index">
          <div :style="{left: item.position }"
               class="transform-translate-x--18px absolute">
            <img v-if="targetInfo.completionRate < item.value"
                 src="@/assets/progress/progress-start.png"
                 class="w-36px h-36px">
            <img v-else
                 src="@/assets/progress/progress-end.png"
                 class="w-36px h-36px">
            <TextShadowAndGradient :text="item.value + '%'"
                                   class="absolute! text-22px" />
          </div>

        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { useSeat } from '@/store/seat'
import { storeToRefs } from 'pinia'

const { targetInfo } = storeToRefs(useSeat())

// 进度条宽度计算
const progressWidth = computed(() => {
  const clampedProgress = Math.max(0, Math.min(100, targetInfo.value.completionRate))
  return `${clampedProgress}%`
})

// 进度节点配置
const progressPoints = [
  { value: 0, position: '0%' },
  { value: 25, position: '25%' },
  { value: 50, position: '50%' },
  { value: 75, position: '75%' },
  { value: 100, position: '100%' }
]
</script>

<style scoped lang="scss">
.progress-container {
  width: 644px;
  height: 12px;
  background: #FFFFFF;
  border-radius: 5px;
  border: 2px solid #FFF0A5;
  .progress-bar {
    height: 8px;
    background: #FFFFFF;
    border-radius: 5px;
    border: 2px solid #FFB027;
    .progress {
     background-image: linear-gradient(-45deg,
      #ffe867 25%,
      #ff611f 25%,
      #ff611f 50%,
      #ffe867 50%,
      #ffe867 75%,
      #ff611f 75%,
      #ff611f 100%);
      background-size: 26px 26px;
      height: 100%;
      border-radius: 5px;
    }
  }
}
</style>
