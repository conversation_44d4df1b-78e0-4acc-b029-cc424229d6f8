<!--
 * @Date         : 2024-06-19 18:14:33
 * @Description  : 个人业绩最高
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="top-container">
    <TextShadowAndGradient text="58.04W"
                           class="text-54px font-bold absolute! top-60px left-270px" />

    <TextShadowAndGradient text="李静月"
                           class="text-54px font-bold italic absolute! top-158px left-255px" />
  </div>
</template>

<script lang='ts' setup>
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'
</script>

<style scoped lang='scss'>
.top-container {
  background: url('@/assets/model/top.png') no-repeat;
  background-size: cover;
  width: 495px;
  height: 250px;
}
</style>
