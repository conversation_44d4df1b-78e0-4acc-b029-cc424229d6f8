<!--
 * @Date         : 2023-06-12 12:31:35
 * @Description  : 月度个人排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="month-rank-container relative">
    <div class="top-95px left-12px absolute overflow-hidden h-450px">
      <Swipe :data-list="rankList"
             :width="472"
             :autoplay="globalStore.cityIsWH ? 12000 : 20000"
             :show-number="10" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { sliceStr, formatAmount } from '@/utils'
import Swipe from '@/components/Swiper.vue'
import { useSeat } from '@/store/seat'
import { useGlobal } from '@/store/global'

const globalStore = useGlobal()
const seatStore = useSeat()

const rankList = computed(() => {
  const target = seatStore.monthPersonRankList.map(item => {
    return [sliceStr(item.name), formatAmount(item.score), sliceStr(item.group)]
  })
  return target
})
</script>

<style scoped lang='scss'>
.month-rank-container {
  height: 553px;
  width: 495px;
  background: url('@/assets/model/month_person_rank.png') no-repeat;
  background-size: 100% 100%;
}
</style>
