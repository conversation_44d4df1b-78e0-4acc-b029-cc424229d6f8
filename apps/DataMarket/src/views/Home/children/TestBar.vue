<!--
 * @Date         : 2024-06-20 16:44:31
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-if="testBtnVisible"
       class="btn-box flexD! w-100vw h-70px">
    <van-button type="primary"
                class="mr-10px!"
                @click="goEffect">全屏转场</van-button>
    <van-button type="primary"
                class="mr-10px!"
                @click="screenDanmuRef.playEncourageHandle()">鼓励词</van-button>
    <van-button type="primary"
                class="mr-10px!"
                @click="fireWorkRef.playTest()">烟花</van-button>
    <van-button type="primary"
                class="mr-10px!"
                @click="useGlobal().changeCity">切换城市</van-button>

    <div class="flexD">
      <div class="c-white text-20px">进度：</div>
      <van-stepper v-model="seatStore.targetInfo.completionRate"
                   input-width="70px" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { getUrlParam } from '@/utils'
import { useSeat } from '@/store/seat'
import { useGlobal } from '@/store/global'

const props = defineProps<{
  screenDanmuRef: any
  fireWorkRef: any
  goEffect: any
}>()

const seatStore = useSeat()
const testBtnVisible = ref(false)

onMounted(() => {
  // 配置url参数test 显示测试按钮
  console.log(Boolean(Number(getUrlParam('test'))))
  testBtnVisible.value = Boolean(Number(getUrlParam('test')))
})
</script>

<style scoped lang='scss'>

</style>
