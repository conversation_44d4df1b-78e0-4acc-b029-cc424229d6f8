<!--
 * @Date         : 2023-06-15 12:23:52
 * @Description  : 顶部条
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="header"
       style="color: white;">
    <div class="header-title">{{ globalStore.city }}全力冲顶数据大盘</div>

    <div class="flexD">
      <div v-if="showFps"
           class="mr-30px">
        fps:{{ fps }}
      </div>
      <div class="time">{{ time }}</div>
    </div>
    <div class="hidden" />
  </div>
</template>

<script setup lang='ts'>
import { useGlobal } from '@/store/global'

const globalStore = useGlobal()

const time = ref('')
const fps = ref<any>()

const showFps = import.meta.env.VITE_ENV === 'test' || import.meta.env.VITE_ENV === 'development'

function test() {
  return `${window.screen.width}, ${window.devicePixelRatio}, ${window.screen.height}, ${window.devicePixelRatio}`
}

function getTime() {
  let date = new Date()
  let sign2 = ':'
  let year = date.getFullYear() // 年
  let month:any = date.getMonth() + 1 // 月
  let day:any = date.getDate() // 日
  let hour:any = date.getHours() // 时
  let minutes:any = date.getMinutes() // 分
  let seconds:any = date.getSeconds() // 秒
  // var weekArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'];
  // var week = weekArr[date.getDay()];
  // 给一位数的数据前面加 “0”
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (day >= 0 && day <= 9) {
    day = '0' + day
  }
  if (hour >= 0 && hour <= 9) {
    hour = '0' + hour
  }
  if (minutes >= 0 && minutes <= 9) {
    minutes = '0' + minutes
  }
  if (seconds >= 0 && seconds <= 9) {
    seconds = '0' + seconds
  }
  time.value = year + '-' + month + '-' + day + ' ' + hour + sign2 + minutes + sign2 + seconds
}

function getFps() {
  const times:any[] = [] // 存储当前的时间数组
  function refreshLoop() {
    window.requestAnimationFrame(() => {
      const now = performance.now() // 使用performance.now()能获取更高的精度
      while (times.length > 0 && times[0] <= now - 1000) {
        times.shift() // 去掉1秒外的时间
      }
      times.push(now)
      fps.value = times.length
      refreshLoop()
      fps.value = times.length
    })
  }

  refreshLoop()
}

onMounted(() => {
  if (showFps) getFps()
  setInterval(() => {
    getTime()
  }, 1000)
})
</script>

<style scoped lang='scss'>
$headerBackground: #222;

// $headerBackground: #141d50;
.header {
  width: 1920px;
  height: 70px;
  background: $headerBackground;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24px;

  .time {
    margin-right: 30px;
    width: 260px;
  }

  .header-title {
    margin-left: 30px;
  }

  .hidden {
    background: $headerBackground;
    width: 100%;
    height: 2px;
    position: absolute;
    bottom: -2px;
  }
}
</style>
