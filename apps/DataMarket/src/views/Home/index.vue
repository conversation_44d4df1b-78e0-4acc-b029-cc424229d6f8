<!--
 * @Date         : 2023-06-09 15:23:20
 * @Description  : 2023暑期数据大盘
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div>
    <HeaderBar />
    <TestBar :screen-danmu-ref="ScreenDanmuRef"
             :fire-work-ref="FireWorkRef"
             :go-effect="goEffect" />

    <PersonPage v-if="globalStore.currentPage === 'person'" />
    <TeamPage v-if="globalStore.currentPage === 'team'" />

    <EffectCut ref="EffectCutRef" />
    <FireWork ref="FireWorkRef" />
    <ScreenDanmu ref="ScreenDanmuRef" />
  </div>
</template>

<script setup lang="ts">
import PersonPage from './PersonPage/index.vue'
import TeamPage from './TeamPage/index.vue'
import EffectCut from './components/effect.vue'
import FireWork from './components/firework.vue'
import ScreenDanmu from './components/screenDanmu.vue'
import HeaderBar from './children/HeaderBar.vue'
import { useGlobal } from '@/store/global'
import TestBar from './children/TestBar.vue'

const globalStore = useGlobal()

const ScreenDanmuRef = ref()
const FireWorkRef = ref()
const EffectCutRef = ref()

function goEffect() {  // 转场
  EffectCutRef.value.start()

  setTimeout(() => {
    globalStore.changeCurrentPage()
  }, 1000)
}

function init() {
  FireWorkRef.value.palyFireWork()
  ScreenDanmuRef.value.lanchDamu()

  setInterval(() => {
    goEffect()
  }, 3 * 60 * 1000)

  setInterval(() => {
    FireWorkRef.value?.palyFireWork()
    ScreenDanmuRef.value?.lanchDamu()
  }, 6 * 1000)
}

onMounted(() => {
  nextTick(() => {
    init()
  })
})
</script>

<style scoped lang='scss'>
.btn-box {
  display: inline-block;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
}
</style>

<style lang="scss">
.no1 {
  width: 186PX;
  height: 35PX;
}

.today-income {
  width: 186PX;
  height: 35PX;
}

.completion-progress {
  width: 191PX;
  height: 43PX;
}
</style>
