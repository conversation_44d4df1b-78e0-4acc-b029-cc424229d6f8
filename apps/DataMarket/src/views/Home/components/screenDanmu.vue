<!--
 * @Date         : 2023-06-13 11:38:16
 * @Description  : 全屏弹幕
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="screen-danmu-container">
    <div ref="TrackRef"
         class="track" />
  </div>
</template>

<script setup lang="ts">
import { getAssetsImage } from '@/utils'
import api from '@/api'

const durationList = [5000, 6000, 7000]
const encourageList = [
  '大吉大利，今晚吃鸡',
  '业绩治百病，大单解千愁，何以解忧，唯有爆单',
  '晒单不是炫耀，成交只是起点，服务没有终点',
  '手里有单，开开开心，天天签单，天天开心！',
  '一手抓业绩，一手抓钞票',
  '企业是我家，发展靠大家'
]
const encourageTimeer = ref<any>(null)  // 20s一次气氛词interval
const playEncourageTimeer = ref<any>(null)  // 倒计时120s 气氛词interval
const unOrderCount = ref(0)   // 倒计时120s 播放气氛词

const TrackRef = ref()

function lanchCss(str) { // css动画播放弹幕
  const bullet = document.createElement('div')
  bullet.innerHTML = str
  TrackRef.value.appendChild(bullet)
  bullet.classList.add('screenDanmuActive')
  bullet.classList.add('screenDanmu')
}

async function lanchJs(str) {  // js播放弹幕
  const bullet = document.createElement('div')
  bullet.innerHTML = str

  const img:any = await lanchImg()
  const bulletBox = document.createElement('div')
  bulletBox.appendChild(img)
  bulletBox.appendChild(bullet)

  bulletBox.classList.add('screen-danmu')
  bulletBox.classList.add(`screen-danmu-style${Math.floor(Math.random() * 8) + 1}`)
  nextTick(() => {
    TrackRef.value?.appendChild(bulletBox)
    animation(bulletBox)
  })
}

function animation(bullet) { // js弹幕动画
  const duration = durationList[Math.floor(Math.random() * 3)]
  const bulletW = bullet.getBoundingClientRect().width
  const windowW = TrackRef.value?.getBoundingClientRect().width
  const journey = bulletW + windowW

  const top = TrackRef.value?.getBoundingClientRect().height - bullet.getBoundingClientRect().height
  bullet.style.top = `${Math.floor(Math.random() * top)}px`
  bullet.style.right = `-${bulletW}px`

  window.requestAnimationFrame(animation)

  let startTime
  function animation(timestamp) {
    if (!startTime) startTime = timestamp
    let progress = (timestamp - startTime) / duration

    bullet.style.right = `${-bulletW + progress * journey}px`
    if (progress >= 1) {
      bullet.remove()
    } else {
      window.requestAnimationFrame(animation)
    }
  }
}

function lanchImg() {  // 随机表情包
  return new Promise((resolve) => {
    const index = Math.floor(Math.random() * 26) + 1

    const img = document.createElement('img')
    img.src = getAssetsImage(`danmu/danmu${index}.png`)
    img.classList.add('screenDanmuImg')

    img.addEventListener('load', () => {
      resolve(img)
    })
  })
}

function playBullet(strArr) {  // 推送弹幕
  const strList = [...strArr]

  if (strArr.length === 0) {  // 鼓励词判断
    timingEncourage()
  } else {
    cancelTimingEncourage()
    cancelEncourage()
  }

  const playInterval = setInterval(() => {  // 一分钟发30条 平均2s一条
    const strInfo = strList.splice(0, 1)[0]

    if (strInfo) {
      const str = `恭喜${strInfo.workerName}成交${strInfo.amount}`
      lanchJs(str)
    } else {
      clearInterval(playInterval)
    }
  }, 2000)
}

function timingEncourage() { // 倒计时播放鼓励词
  if (playEncourageTimeer.value) return // 多次推送0成单数组 避免覆盖计时器

  playEncourageTimeer.value = setInterval(() => {
    unOrderCount.value++
    if (unOrderCount.value >= 120) {
      playEncourage()

      cancelTimingEncourage()
    }
  }, 1000)
}

function cancelTimingEncourage() { // 取消播放鼓励词倒计时
  unOrderCount.value = 0
  if (playEncourageTimeer.value) {
    clearInterval(playEncourageTimeer.value)
    playEncourageTimeer.value = null
  }
}

function playEncourage() { // 开始定时播放鼓励词 20s定时器
  if (encourageTimeer.value) return

  playEncourageHandle()
  encourageTimeer.value = setInterval(() => {
    playEncourageHandle()
  }, 20000)
}

function cancelEncourage() { // 取消鼓励词定时 取消20s定时器
  if (encourageTimeer.value) {
    clearInterval(encourageTimeer.value)
    encourageTimeer.value = null
  }
}

function playEncourageHandle() { // 播放鼓励词
  const index = Math.floor(Math.random() * encourageList.length)
  const str = encourageList[index]
  lanchJs(str)
}

async function lanchDamu() { // 发送全局小单弹幕
  const orderCursor = localStorage.getItem('orderCursor')
  const data = await api.getOrderList({
    cursor: orderCursor || 0,
    limit: 30
  })

  localStorage.setItem('orderCursor', data.cursor)

  playBullet(data.list)
}

defineExpose({ playEncourageHandle, playBullet, lanchDamu })
</script>

<style lang='scss' scoped>
.screen-danmu-container {
  position: fixed;
  top: 0;
  z-index: 101;
  width: 100%;
  overflow: hidden;

  .track {
    width: 100%;
    height: calc(100vh - 70px);
    position: fixed;
    top: 70px;
    z-index: 101;
  }
}
</style>

<style lang="scss">
$commonFontFamily: system-ui, -apple-system, BlinkMacSystemFont;

.screen-danmu {
  font-size: 30px;
  color: white;
  white-space: nowrap;
  position: absolute;
  display: inline-block;
  padding: 3px 20px;
  font-family: $commonFontFamily;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  >div {
    margin-left: 10px;
  }
}

.screenDanmuActive {
  left: -100%;
  animation-name: danmu;
  animation-duration: 10s;
  animation-timing-function: linear;
  animation-fill-mode: forwards
}

.screenDanmuImg {
  height: 35px;
}

@keyframes danmu {
  from {
    left: -100%;
  }

  to {
    left: 100%;
  }
}

.screen-danmu-style1 {
  background: #0ce4fc;
  color: #2476ec;
}
.screen-danmu-style2 {
  background: #fc8c4c;
  color: white;
}
.screen-danmu-style3 {
  background: #cbecf4;
  color: #2cb6fc;
}
.screen-danmu-style4 {
  background: #10a4fc;
  color: white;
}
.screen-danmu-style5 {
  background: #fc441c;
  color: white;
}
.screen-danmu-style6 {
  background: #9c14fc;
  color: white;
}
.screen-danmu-style7 {
  background: #f4d4dc;
  color: #f490b8;
}
.screen-danmu-style8 {
  background: #349cfc;
  color: white;
}

</style>
