<!--
 * @Date         : 2023-06-09 18:04:02
 * @Description  : 转场开闭幕效果
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="effect-container">
    <div v-if="effectStep1"
         class="top"
         :class="{topStep1: effectStep1, topStep4: effectStep4}" />
    <div v-if="effectStep1"
         class="bottom"
         :class="{bottomStep1: effectStep1, bottomStep4: effectStep4}" />
    <div v-if="effectStep2"
         class="left"
         :class="{leftStep2: effectStep2, leftStep3: effectStep3}" />
    <div v-if="effectStep2"
         class="right"
         :class="{rightStep2: effectStep2, rightStep3: effectStep3}" />
    <div v-if="progress"
         :class="{progress: progress}" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      effectStep1: false,
      effectStep2: false,
      effectStep3: false,
      effectStep4: false,
      progress: false,
    }
  },
  methods: {
    start() {
      this.effectStep1 = true
      setTimeout(() => {
        this.effectStep2 = true
      }, 300)
      setTimeout(() => {
        this.progress = true
      }, 600)
      setTimeout(() => {
        this.progress = false
        this.effectStep3 = true
      }, 900)
      setTimeout(() => {
        this.effectStep4 = true
      }, 1200)
      setTimeout(() => {
        this.reset()
      }, 1500)
    },
    reset() {
      this.effectStep1 = false
      this.effectStep2 = false
      this.effectStep3 = false
      this.effectStep4 = false
    }
  }
}
</script>

<style scoped lang='scss'>
.effect-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 999;
  top: 0;
}

.top, .bottom {
  width: 100vw;
  height: 49.5vh;
  position: absolute;
  z-index: 10;
  background-color: white;
  animation-fill-mode: forwards;
  // opacity: .5;
}

.left, .right {
  width: 45vw;
  height: 100vh;
  position: absolute;
  z-index: 10;
  background-color: white;
  animation-fill-mode: forwards;
  // opacity: .5;
}

.top {
  top: 0;
  transform: translateY(-100%);
}

.bottom {
  bottom: 0;
  transform: translateY(100%);
}

.left {
  top: 0;
  left: 0;
  transform: translateX(-100%);
}

.right {
  top: 0;
  right: 0;
  transform: translateX(100%);
}

.topStep1 {
  animation-name: topMoveStep1;
  animation-duration: 0.3s;
}

.bottomStep1 {
  animation-name: bottomMoveStep1;
  animation-duration: 0.3s;
}

.leftStep2 {
  animation-name: leftMoveStep2;
  animation-duration: 0.3s;
}

.rightStep2 {
  animation-name: rightMoveStep2;
  animation-duration: 0.3s;
}

.topStep4 {
  animation-name: topMoveStep4;
  animation-duration: 0.3s;
}

.bottomStep4 {
  animation-name: bottomMoveStep4;
  animation-duration: 0.3s;
}

.leftStep3 {
  animation-name: leftMoveStep3;
  animation-duration: 0.3s;
}

.rightStep3 {
  animation-name: rightMoveStep3;
  animation-duration: 0.3s;
}

@keyframes topMoveStep1 {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes bottomMoveStep1 {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes leftMoveStep2 {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes rightMoveStep2 {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes topMoveStep4 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-100%);
  }
}

@keyframes bottomMoveStep4 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

@keyframes leftMoveStep3 {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

@keyframes rightMoveStep3 {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}

.progress {
  height: 1%;
  position: absolute;
  top: 49.5%;
  left: 45%;
  background-color: #1c2871;
  z-index: 11;
  animation-fill-mode: forwards;
  animation-name: progressMove;
  animation-duration: 0.6s;
}

@keyframes progressMove {
  from {
    width: 0;
  }

  50% {
    width: 10%;
  }

  to {
    width: 0;
  }
}
</style>
