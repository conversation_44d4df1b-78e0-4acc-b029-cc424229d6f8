<!--
 * @Date         : 2023-06-09 18:57:44
 * @Description  : 烟花
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div v-if="showFire"
       class="firework-container">
    <div class="fireworkMask" />
    <div v-if="showModel"
         class="model-box animate__animated animate__bounceIn">
      <div class="model relative!">
        <TextShadowAndGradient :text="info.workerName "
                               class="text-69px font-bold absolute top-216px left-577px" />
        <TextShadowAndGradient :text="`¥${info.amount}`"
                               class="text-96px font-bold italic absolute top-370px left-500px" />
      </div>
    </div>

    <FireWorkVideo v-if="showFire"
                   :double="false"
                   class="z-110!" />
  </div>
</template>

<script setup lang='ts'>
import api from '@/api'
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'
import FireWorkVideo from '@/components/FireWorkVideo.vue'
import 'animate.css'

const showFire = ref(false)
const showModel = ref(false)
const info = reactive({
  workerName: '',
  amount: 0
})

function play(arr) {
  if (arr.length === 0) return
  let orderList = [...arr]
  const orderInterval = setInterval(() => {
    handle()
  }, 6000)  // 6秒播放一次
  handle()
  function handle() {
    const orderInfo = orderList.splice(0, 1)[0]
    playHandle(orderInfo)

    if (orderList.length === 0) {
      if (orderInterval) clearInterval(orderInterval)
    }
  }
}

function playHandle(val) {
  info.workerName = val.workerName
  info.amount = val.amount

  showFire.value = true
  setTimeout(() => {
    showModel.value = true
  }, 2000)  // 2s后显示信息框
  setTimeout(() => {
    showFire.value = false
    showModel.value = false
  }, 5900)  // 6s后整体结束
}

async function palyFireWork() {  // 发送大单烟花
  const bigOrderCursor = localStorage.getItem('bigOrderCursor')
  const data = await api.getBigOrderList({
    cursor: bigOrderCursor || 0,
    limit: 6
  })

  localStorage.setItem('bigOrderCursor', data.cursor)

  play(data.list)
}

function playTest() {
  play([{ workerName: '邱卓然', amount: 12345 }, { workerName: '邱卓然1', amount: 11345 }, { workerName: '邱卓然2', amount: 12345 }, { workerName: '邱卓然3', amount: 12345 }, { workerName: '邱卓然4', amount: 12345 }, { workerName: '邱卓然5', amount: 12345 }])
}

defineExpose({ play, playTest, palyFireWork })
</script>

<style scoped lang='scss'>
.firwork-container {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
}

.fireworkMask {
  width: 100vw;
  height: 100vh;
  background-color: black;
  position: fixed;
  opacity: 0.4;
  top: 0;
  z-index: 109;
}

.model {
  background: url('@/assets/model/firework.png') no-repeat;
  width: 1350px;
  height: 621px;
  background-size: cover;
}

.model-box {
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 111;
  width: 100vw;
  height: 100vh;
}
</style>
