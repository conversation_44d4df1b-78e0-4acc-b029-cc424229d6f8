<!--
 * @Date         : 2024-06-19 14:03:16
 * @Description  : 团队记录页
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="content-container relative">
    <FireWorkVideo v-if="useSeat().completionOver100"
                   :double="true" />

    <div class="z-100 relative">
      <TeamTop class="absolute! top-118px left-60px" />
      <MonthTeamRank class="absolute! top-399px left-60px" />

      <TeamRank class="absolute! top-166px left-596px" />
      <TodayTeamTop class="absolute! top-532px left-596px" />
      <WeekTeamTop class="absolute! top-754px left-596px" />

      <WeekTeamRank class="absolute! top-118px right-60px" />
      <Danmu class="absolute! top-485px right-60px" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useSeat } from '@/store/seat'
import { useTeam } from '@/store/team'
import TeamTop from './children/TeamTop.vue'
import TodayTeamTop from './children/TodayTeamTop.vue'
import WeekTeamTop from './children/WeekTeamTop.vue'
import TeamRank from './children/TeamRank.vue'
import Danmu from './children/Danmu.vue'
import WeekTeamRank from './children/WeekTeamRank.vue'
import MonthTeamRank from './children/MonthTeamRank.vue'
import FireWorkVideo from '@/components/FireWorkVideo.vue'

onMounted(() => {
  useTeam().getTeamData()
})
</script>

<style scoped lang='scss'>
.content-container {
  overflow: hidden;
  height: 1010px;
  width: 1920px;
  background-image: url('@/assets/team_bg.png');
  background-size: 100% 100%;
}
</style>
