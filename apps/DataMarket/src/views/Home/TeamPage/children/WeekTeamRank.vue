<!--
 * @Date         : 2024-06-20 15:07:54
 * @Description  : 小组当配转化排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="week-team-rank-container relative">
    <div class="top-92px left-12px absolute overflow-hidden h-220px">
      <Swipe :data-list="rankList"
             :width="462"
             :autoplay="globalStore.cityIsWH ? 30000 : 0"
             :show-number="5" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { sliceStr, formatAmount } from '@/utils'
import Swipe from '@/components/Swiper.vue'
import { useTeam } from '@/store/team'
import { useGlobal } from '@/store/global'

const globalStore = useGlobal()
const teamStore = useTeam()

const rankList = computed(() => {
  const target = teamStore.monthRankList.map(item => {
    return [sliceStr(item.group, 3), item.userCount, formatAmount(item.amount)]
  })
  return target
})
</script>

<style scoped lang='scss'>
.week-team-rank-container {
  height: 334px;
  width: 493px;
  background: url('@/assets/model/week_team_rank.png') no-repeat;
  background-size: 100% 100%;
}
</style>
