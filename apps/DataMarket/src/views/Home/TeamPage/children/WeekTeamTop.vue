<!--
 * @Date         : 2023-06-13 15:25:38
 * @Description  : 本周小组业绩总冠军
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="week-top-container relative">
    <TextShadowAndGradient :text="sliceStr(teamStore?.weekTeamTopInfo?.group, 3)"
                           class="text-34px italic font-bold top-106px left-96px absolute" />

    <span class="c-white text-20px font-600 top-84px left-560px absolute">{{ sliceStr(teamStore?.weekTeamTopInfo?.team, 4) }}</span>
    <span class="c-white text-20px font-600 top-126px left-560px absolute">{{ formatAmount(teamStore?.weekTeamTopInfo?.amount) }}</span>

  </div>
</template>

<script setup lang="ts">
import { useTeam } from '@/store/team'
import { sliceStr, formatAmount } from '@/utils'
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'

const teamStore = useTeam()
</script>

<style scoped lang='scss'>
.week-top-container {
  background: url('@/assets/model/week_team_top.png') no-repeat;
  background-size: 100% 100%;
   width: 728px;
  height: 194px;
}
</style>
