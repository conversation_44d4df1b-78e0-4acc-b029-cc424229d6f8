<!--
 * @Date         : 2024-06-19 21:56:06
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="top-container relative">
    <TextShadowAndGradient text="395.57W"
                           class="text-50px font-bold absolute! top-65px left-150px" />

    <TextShadowAndGradient text="葱王队"
                           class="text-40px font-bold italic absolute! top-162px left-180px" />
  </div>
</template>

<script lang='ts' setup>
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'
</script>

<style scoped lang='scss'>
.top-container {
  background: url('@/assets/model/team_top.png') no-repeat;
  background-size: cover;
  width: 493px;
  height: 248px;
}
</style>
