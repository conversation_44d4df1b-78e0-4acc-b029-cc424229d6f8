<!--
 * @Date         : 2023-06-12 17:13:46
 * @Description  : 今日小组业绩冠军
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="today-top-container relative">
    <TextShadowAndGradient :text="sliceStr(teamStore?.todayTeamTopInfo?.group, 3)"
                           class="text-34px italic font-bold top-106px left-96px absolute" />

    <span class="c-white text-20px font-600 top-84px left-560px absolute">{{ sliceStr(teamStore?.todayTeamTopInfo?.team, 4) }}</span>
    <span class="c-white text-20px font-600 top-126px left-560px absolute">{{ formatAmount(teamStore?.todayTeamTopInfo?.amount) }}</span>

  </div>
</template>
 
<script setup lang="ts">
import { useTeam } from '@/store/team'
import { sliceStr, formatAmount } from '@/utils'
import TextShadowAndGradient from '@/components/TextShadowAndGradient.vue'

const teamStore = useTeam()
</script>

<style scoped lang='scss'>
.today-top-container {
  background: url('@/assets/model/today_team_top.png') no-repeat;
  background-size: 100% 100%;
  width: 728px;
  height: 194px;
}
</style>
