<!--
 * @Date         : 2023-06-12 18:23:52
 * @Description  : 月度小组排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="month-team-rank-container">
    <div class="top-92px left-12px absolute overflow-hidden h-450px">
      <van-swipe vertical
                 autoplay="20000"
                 :show-indicators="false">
        <van-swipe-item v-for="(ite, idx) in rankListHandle"
                        :key="idx">
          <div v-for="(item, index) of ite"
               :key="index"
               class="rank-item"
               :class="{ top: checkTop(idx, index) }">
            <div v-if="checkTop(idx, index)"
                 class="rank-icon-box">
              <Medal :rank="index + 1" />
            </div>
            <div v-else>NO.{{ (idx * 10) + index + 1 }}</div>
            <div>{{ sliceStr(item.group, 10) }}</div>
            <div>{{ formatAmount(item.amount) }}</div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { sliceStr, formatAmount, getAssetsImage } from '@/utils'
import { useTeam } from '@/store/team'
import Medal from '@/components/Medal.vue'
import Progress from '@/components/Progress.vue'

const teamStore = useTeam()

const rankListHandle = computed(() => {
  const target = teamStore.monthTeamRankList.reduce((prev, curr, index) => {
    const i = Math.floor(index / 10)
    prev[i] = [...prev[i] || [], ...[curr]]
    return prev
  }, [])
  return target
})

function checkTop(idx, index) {
  return idx === 0 && (index === 0 || index === 1 || index === 2)
}
</script>

<style scoped lang='scss'>
.month-team-rank-container {
  background: url('@/assets/model/month_team_rank.png') no-repeat;
  background-size: 100% 100%;
  width: 493px;
  height: 549px;

  :deep(.van-swipe__track) {
    width: 472px;
  }

  .rank-item {
    display: flex;
    position: relative;
    align-items: center;
    margin-bottom: -2.2px;

    font-size: 20px;
    color: white;
    width: 100%;
    > div {
      text-align: center;
      position: absolute;
    }
    > div:nth-child(1) {
      left: 28px;
      width: 60px;
      display: flex;
      justify-content: center;
    }
    > div:nth-child(2) {
      left: 110px;
      width: 210px;
    }
    > div:nth-child(3) {
      width: 130px;
      left: 320px;
    }
  }

  .rank-item {
    background: url('@/assets/line_bg.png') no-repeat;
    background-size: 100% 100%;
    height: 46px;
    width: 472px;
  }
}

.top {
  color: #FFEE9F !important;
  :deep(div) {
    color: #FFEE9F !important;
  }
}
</style>
