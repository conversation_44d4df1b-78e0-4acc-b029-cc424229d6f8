<!--
 * @Date         : 2023-06-12 17:33:39
 * @Description  : 洋葱我想对你说
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="danmu-container">
    <div ref="DanmuBoxRef"
         class="danmu-box">
         <!-- <div class="danmu danmu-style1">
        <span class="icon" />
        <div>
          你好，我是洋葱，很高兴认识你！
        </div>
      </div> -->
    </div>
  </div></template>

<script setup lang='ts'>
const slogan = [
  '拼一次，富三代，拼命才能不失败！ --铸星队',
  '一点寒芒先到，随后业绩长虹！ --寒战队',
  '要成功，先发疯，不顾一切往前冲！ --乘风队',
  '无敌，无敌，所向披靡！ --无敌队',
  '携手冲、一起赢！ --汪汪队',
  '乘风破浪创佳绩，继往开来续辉煌！ --扬帆起航队',
  '分享青春，共筑梦想 勇往直前，加油！ --筑梦队',
  '启明之星，冉冉升起，勇往直前，一飞冲天 ！ --启星队',
  '龙争虎斗彼岸花，星火四组发发发发！ --所向披靡',
  '扬帆起航，百日奋战，百日辉煌！ --扬帆起航',
  '吃饭睡觉，赚钞票！ --钞票队',
  '破浪前行，勇闯佳绩！ --破浪前行',
  '激情澎湃，十组不败。斗志昂扬，十组最强！ --钞人队',
  '一鼓作气，创造佳绩！ --钞钞队',
  '奋力拼搏，勇于攀登！ --攀登者队',
  '浩瀚星空，以我居中，暑假洋葱，卖课最疯！ --星空队',
  '每天都超标 天天创新高！ --地表最强队',
  '勇攀珠峰，梦之队带头冲！ --梦之队',
  '状态满格，一路向前力拔山河天！ --一路向钱',
  '全力以赴，全员致富！ --爆富队',
  '勇往直前，永不止步！ --飞跃队',
  '龙年行大运，业绩忙不停！ --龙腾队',
  '狭路相逢勇者胜！ --勇者队',
  '天天开单，年年发财！ --天财队',
  '天霸动霸Tua！ --天霸队',
  '天天爆单，月月过万！ --天爆队',
  '天冠，天冠，力争夺冠！ --天冠队',
  '一飞冲天，一起称王！ --葱王队',
  '剑锋所指，所向披靡！ --亮剑队',
  '与天斗，胜半子，加油，干！ --胜天半子队',
  '乾坤未定，你我皆是黑马，加油加油加油！ --巅峰队',
  '辉煌出击，所向披靡！ --辉煌队',
  '燃动暑假，使命必达！ --凌云队',
  '扬帆暑假，加薪加薪！ --加薪队',
  '暑假早规划，逆袭当学霸 怒鲨在洋葱，永远是巅峰！ --怒鲨队',
  '完标可行！扶摇必成！ --扶摇队'
]

const durationList = [8000, 9000, 10000]

const DanmuBoxRef = ref()

function lanch(str) {
  const type = Math.floor(Math.random() * 6) + 1

  const bulltBox = document.createElement('div')
  bulltBox.classList.add('danmu')
  bulltBox.classList.add(`danmu-style${type}`)


  const bullet = document.createElement('div')
  bullet.innerHTML = str

  const iconDom = document.createElement('span')
  iconDom.classList.add('icon')

  bulltBox.appendChild(iconDom)
  bulltBox.appendChild(bullet)

  DanmuBoxRef?.value?.appendChild(bulltBox)

  animation(bulltBox)
}

function animation(bullet) {
  const duration = durationList[Math.floor(Math.random() * 3)]
  const bulletW = bullet.getBoundingClientRect().width * 2
  const danmuBoxW = DanmuBoxRef?.value?.getBoundingClientRect().width * 2
  const journey = bulletW + danmuBoxW

  const top = DanmuBoxRef?.value?.getBoundingClientRect().height - bullet.getBoundingClientRect().height
  bullet.style.top = `${Math.floor(Math.random() * top)}px`
  bullet.style.right = `-${bulletW}px`

  window.requestAnimationFrame(animation)

  let startTime
  function animation(timestamp) {
    if (!startTime) startTime = timestamp
    let progress = (timestamp - startTime) / duration

    bullet.style.right = `${-bulletW + progress * journey}px`
    if (progress >= 1) {
      bullet.remove()
    } else {
      window.requestAnimationFrame(animation)
    }
  }
}

function push() {
  const str = slogan[Math.floor(Math.random() * slogan.length)]

  lanch(str)
}


onMounted(() => {
  nextTick(() => {
    setInterval(() => {
      push()
    }, 1000)
  })
})

defineExpose({ play: push })
</script>

<style lang='scss'>
.danmu-container {
  width: 493px;
  height: 463px;
  background: url('@/assets/model/danmu.png') no-repeat;
  background-size: 100% 100%;
}

.danmu-box {
  position: relative;
  overflow: hidden;
  top: 90px;
  height: 370px;
  width: calc(100% - 10px);
  left: 5px;
}

.danmu {
  display: inline-flex;
  justify-content: center;
  align-items: center;

  font-size: 20px;
  color: white;
  white-space: nowrap;
  position: absolute;
  top: 0;


  .icon {
    width: 66px;
    height: 51px;
    background-size: cover !important;
    position: relative;
    left: 35px;
  }

  > div {
    border-radius: 18px;
    padding: 8px 20px;
    padding-left: 45px;
  }
}

.danmu-style1 {
  .icon {
    background: url('@/assets/icon/danmu1.png') no-repeat;
  }

  >div {
    background: #A668FC;
  }
}

.danmu-style2 {
  .icon {
    background: url('@/assets/icon/danmu2.png') no-repeat;
  }

  >div {
    background: #586BFD;
  }
}

.danmu-style3 {
  .icon {
    background: url('@/assets/icon/danmu3.png') no-repeat;
  }

  >div {
    background: #2ED36C;
  }
}

.danmu-style4 {
  .icon {
    background: url('@/assets/icon/danmu4.png') no-repeat;
  }

  >div {
    background: #FF8042;
  }
}

.danmu-style5 {
  .icon {
    background: url('@/assets/icon/danmu5.png') no-repeat;
  }

  >div {
    background: #E74CB6;
  }
}

.danmu-style6 {
  .icon {
    background: url('@/assets/icon/danmu6.png') no-repeat;
  }

  >div {
    background: #1CB7FF;
  }
}
</style>
