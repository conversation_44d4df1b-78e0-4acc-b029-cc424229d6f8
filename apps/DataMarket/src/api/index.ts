import { AxiosClass } from '@guanghe-pub/nexus-axios'
import { useGlobal } from '@/store/global'

const Axios = new AxiosClass({
  reductData: false
})

const api = {
  getSeatList(): Promise<any> {
    return Axios.get(`${import.meta.env.VITE_HOST_TELESALE_API}dashboard/rank/synthesis/list`, {
      base: useGlobal().cityCode
    })
  },

  getTeamList(): Promise<any> {
    return Axios.get(`${import.meta.env.VITE_HOST_TELESALE_API}dashboard/rank/group/synthesis/list`, {
      base: useGlobal().cityCode
    })
  },

  getOrderList(par): Promise<any> {
    return Axios.get(`${import.meta.env.VITE_HOST_TELESALE_API}dashboard/rank/order/list`, {
      ...par,
      base: useGlobal().cityCode
    })
  },

  getBigOrderList(par): Promise<any> {
    return Axios.get(`${import.meta.env.VITE_HOST_TELESALE_API}dashboard/rank/big_order/list`, {
      ...par,
      base: useGlobal().cityCode
    })
  }
}

export default { ...api }
