<!--
 * @Date         : 2024-04-03 12:15:15
 * @Description  : 待回访列表
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="px-20px">
    <div class="flex justify-between">
      <Form inline>
        <el-form-item>
          <el-input v-model="orderId"
                    clearable
                    type="number"
                    placeholder="请输入工单编号搜索" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary"
                     @click="search">搜索</el-button>
        </el-form-item>
      </Form>

      <el-form-item>
        <el-button type="primary"
                   @click="BatchTransferVisible=true">批量转移</el-button>
      </el-form-item>
    </div>

    <Table ref="ReturnListTable"
           :get-list="api.getOrderReturnList"
           un-mounted>
      <el-table-column type="selection"
                       :selectable="(row) => row.status === 'todo'"
                       width="55" />
      <el-table-column label="工单编号"
                       prop="orderId" />
      <el-table-column label="回访时间"
                       prop="nextReturnAt" />
      <el-table-column label="备注"
                       prop="desc" />
      <FilterColumn :table-ref="ReturnListTable"
                    label="状态"
                    prop="status"
                    :option-list="[
                      {text: '待提醒', val: 'todo'},
                      {text: '已提醒', val: 'called'},
                      {text: '已完成', val: 'success'},
                    ]" />
      <el-table-column label="转移人"
                       prop="divertFrom" />
      <el-table-column label="操作">
        <template #default="{row}">
          <el-link v-if="row.status!=='success'"
                   type="primary"
                   :underline="false"
                   @click="complete(row)">已跟进</el-link>
        </template>
      </el-table-column>
    </Table>

    <BatchTransferDialog v-model:visible="BatchTransferVisible"
                         :table-ref="ReturnListTable" />
  </div>
</template>

<script lang='ts' setup>
import Form from '@/components/Form/index.vue'
import Table from '@/components/Table/index.vue'
import FilterColumn from '@/components/Table/components/FilterColumn.vue'
import BatchTransferDialog from '../components/BatchTransferDialog.vue'
import api from '@/api'
import { useRoute } from 'vue-router'
import { useGlobalState } from '../store'

const route = useRoute()
const ReturnListTable = ref()

const BatchTransferVisible = ref(false)

onMounted(() => {
  const query:any = route.query

  useGlobalState().TableRef.value = ReturnListTable.value
  ReturnListTable.value.search({
    email: query.email
  })
})

async function complete(info) {
  await api.completeOrderReturn({
    id: info.id
  })

  ElMessage.success('已跟进成功')
  ReturnListTable.value.update()
}

const orderId = ref('')
function search() {
  ReturnListTable.value.search({
    orderId: orderId.value
  })
}
</script>

<style scoped lang='scss'>

</style>
