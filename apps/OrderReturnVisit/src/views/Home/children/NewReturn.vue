<!--
 * @Date         : 2024-04-03 10:52:33
 * @Description  : 新增回访
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-350px px-20px">
    <Form ref="FormRef"
          v-model="newReturnForm">
      <el-form-item label="工单编号"
                    prop="orderId"
                    :rules="[{required: true, message: '请输入工单编号'}]">
        <el-input v-model="newReturnForm.orderId"
                  type="number"
                  placeholder="请输入工单编号" />
      </el-form-item>

      <el-form-item label="下次回访日期"
                    prop="time"
                    :rules="[{required: true, message: '请输入下次回访时间'}, {validator:validTime, trigger: 'blur'}]">
        <el-date-picker v-model="newReturnForm.time"
                        type="datetime"
                        :disabled-date="disableDate"
                        placeholder="请选择下次回访日期" />
      </el-form-item>

      <el-form-item label="备注">
        <el-input v-model="newReturnForm.desc"
                  maxlength="300"
                  show-word-limit
                  :autosize="{ minRows: 2 }"
                  type="textarea" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary"
                   @click="submit">提交</el-button>
      </el-form-item>
    </Form>
  </div>
</template>

<script lang='ts' setup>
import Form from '@/components/Form/index.vue'
import api from '@/api'
import { useRoute } from 'vue-router'
import { useGlobalState } from '../store'
import dayjs from 'dayjs'

const route = useRoute()

const newReturnForm = reactive({
  orderId: '',
  time: '',
  desc: ''
})

function disableDate(time) {
  if (dayjs().isAfter(dayjs(time), 'day')) return true
  else return false
}

const validTime = (rule: any, value: any, callback: any) => {
  if (dayjs().add(5, 'm').isAfter(dayjs(newReturnForm.time))) {
    callback(new Error('预约回访时间过近，请重新选择哦～'))
  }
  callback()
}

const FormRef = ref()
async function submit() {
  await FormRef.value.validate()
  const query:any = route.query

  const res = await api.createOrderReturn({
    orderId: newReturnForm.orderId,
    email: query.email,
    nextReturnAt: String(new Date(newReturnForm.time).getTime()).slice(0, -3),
    desc: newReturnForm.desc,
  })

  ElMessage.success('新增回访成功')

  newReturnForm.orderId = ''
  newReturnForm.time = ''
  newReturnForm.desc = ''

  useGlobalState().TableRef.value.update()
  useGlobalState().activeName.value = '待回访列表'
}

</script>

<style scoped lang='scss'>

</style>
