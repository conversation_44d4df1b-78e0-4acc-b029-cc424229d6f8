<!--
 * @Date         : 2024-04-03 14:05:23
 * @Description  : 批量转移提醒弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog v-model="visible"
             title="批量转移提醒">

    <Form ref="FormRef"
          v-model="form">
      <el-form-item label="接受人"
                    prop="email"
                    :rules="[{required: true, message: '请输入email'}, {validator: validEmail, trigger: 'blur'} ]">
        <el-input v-model="form.email" />
      </el-form-item>
    </Form>

    <template #footer>
      <el-button type="danger"
                 @click="visible=false">取消</el-button>
      <el-button type="primary"
                 @click="divert">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import Form from '@/components/Form/index.vue'
import api from '@/api'

const visible = defineModel<boolean>('visible')
const props = defineProps<{
  tableRef: any
}>()

const FormRef = ref()
const form = reactive({
  email: ''
})

async function divert() {
  await FormRef.value.validate()

  try {
    await api.divertOrderReturn({
      ids: props.tableRef.TableRef.getSelectionRows().map(item => item.id),
      divertFrom: form.email
    })
  } catch (error) {
    ElMessage.error('转移失败')
    return
  }

  ElMessage.success('转移成功')
  props.tableRef.update()
  visible.value = false
}

function validEmail(rule, value, callback) {
  const emailReg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  console.log(value, value === '')
  if (!value) {
    callback(new Error('请输入邮箱'))
  }
  if (!emailReg.test(value)) {
    callback(new Error('请输入正确的邮箱'))
  }

  if (!value.endsWith('@guanghe.tv')) {
    callback(new Error('请输入guanghe.tv邮箱'))
  }
  callback()
}
</script>

<style scoped lang='scss'>

</style>
