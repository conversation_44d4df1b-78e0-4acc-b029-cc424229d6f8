<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  : home
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="px-10px">
    <h2>工单回访登记</h2>
    <el-config-provider :locale="zhCn">
      <el-tabs v-model="activeName"
               type="card">
        <el-tab-pane label="新增回访"
                     name="新增回访">
          <NewReturn />
        </el-tab-pane>
        <el-tab-pane label="待回访列表"
                     name="待回访列表">
          <ReturnList />
        </el-tab-pane>
      </el-tabs>
    </el-config-provider>
  </div>
</template>

<script lang='ts' setup>
import NewReturn from './children/NewReturn.vue'
import ReturnList from './children/ReturnList.vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useGlobalState } from './store'

const { activeName } = useGlobalState()
</script>

<style scoped lang='scss'>
.text1 {
  border-right: 1px solid #3FBAFF;
}

.text2 {
  border-top: 1px solid #3FBAFF;
}
</style>
