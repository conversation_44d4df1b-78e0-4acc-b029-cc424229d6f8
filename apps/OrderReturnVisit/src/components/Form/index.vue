<!--
 * @Date         : 2024-01-08 15:49:17
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-form ref="FormRef"
           :model="model"
           label-width="120px"
           @submit.prevent>
    <slot />
  </el-form>
</template>

<script lang='ts' setup>
import { defineModel } from 'vue'
import type {  FormInstance } from 'element-plus'
import assignment from './script/assignment'

const FormRef = ref()
const model = defineModel<Record<string, any>>()

// 验证
function validateFn(formEl: FormInstance | undefined) {
  if (!formEl) return
  return new Promise((resolve, inject) => {
    formEl.validate((valid, fields) => {
      if (valid) {
        resolve(valid)
      } else {
        inject(Error('error submit!' + fields))
      }
    })
  })
}

async function validate() {
  await validateFn(FormRef.value)
}

defineExpose({ validate, assignment })
</script>

<style scoped lang='scss'>

</style>
