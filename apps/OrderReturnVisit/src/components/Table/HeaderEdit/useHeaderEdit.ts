export function useHeaderEdit() {
  const HeaderEditRef = ref()
  const headerEditVisible = ref(false)
  provide('headerEditVisible', headerEditVisible)

  const showHeaderKeyList = ref<string[]>([])
  provide('showHeaderKeyList', showHeaderKeyList)

  function checkShow(key: string) {
    return showHeaderKeyList.value.includes(key)
  }

  return { HeaderEditRef, headerEditVisible, checkShow }
}

export interface HeaderEditConfig {
  localKey: string
  localList: {
    label: string
    key: string
    defaultUnShow?: boolean
  }[]
}
