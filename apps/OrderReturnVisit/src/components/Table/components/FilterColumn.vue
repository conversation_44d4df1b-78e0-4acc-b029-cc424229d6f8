<!--
 * @Date         : 2023-11-14 15:56:11
 * @Description  : 可筛选列
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-table-column label="处理状态">
    <template #default="{row}">
      {{ getTextByVal(getValByProp(row), row) }}
    </template>

    <template #header>
      <div class="flex justify-start items-center">
        <el-dropdown>
          <span class="flexD cursor-pointer c-[--el-color-info]">
            {{ label }}-{{ handleText }}
            <el-icon class="ml-5px"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="checkHandleState('all', '全部')">全部</el-dropdown-item>
              <el-dropdown-item v-for="(item, index) in optionList"
                                :key="index"
                                @click="checkHandleState(item.val, item.text)">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </el-table-column>
</template>

<script lang='ts' setup>
import { ArrowDown } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
  formatter?: any
  tableRef: any
  label: string
  prop: string
  paramKey?: string // 避免查询字段与prop不一致 表格查询以此字段为主
  optionList: {
    text: string
    val: any
    showKey?: any // 避免显示字段与key字段不一致 显示以此字段为主
  }[]
  cb?: Function
}>(), {
  formatter: () => {}
})

function getTextByVal(val, row) {
  const target = props.optionList.find(item => (item.showKey ?? item.val) === val)
  if (props.formatter) {
    if (props.formatter(row)) return ''
  }
  return target?.text
}

function getValByProp(row) {
  if (props.prop.includes('.')) {
    return props.prop.split('.').reduce((obj, property) => {
      return obj[property]
    }, row)
  } else {
    return row[props.prop]
  }
}

// 处理状态
const handleState = ref('all')
const handleText = ref('全部')

function checkHandleState(state:string, text: string) {
  if (handleState.value === state) return

  handleState.value = state
  handleText.value = text
  props.tableRef.searchHandle({
    [props.paramKey ?? props.prop]: handleState.value === 'all' ? null : handleState.value
  })

  if (props.cb) props.cb({ handleState: handleState.value, handleText: handleText.value })
}

function checkAll() {
  checkHandleState('all', '全部')
}

defineExpose({
  handleState, handleText, checkAll
})
</script>

<style scoped lang='scss'>

</style>
