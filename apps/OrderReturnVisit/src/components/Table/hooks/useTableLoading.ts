export default function() {
  const loading = ref<boolean>(false)

  async function checkLoading<T>(cb: () => Promise<T>): Promise<T> {
    setTimeout(() => {
      loading.value = false
    }, 20000)

    let res
    loading.value = true
    try {
      res = await cb()
    } catch (error) {
      console.error(error)
    }
    loading.value = false
    return res
  }

  return { checkLoading, loading }
}
