import Axios from './axios'

/**
 * @description: 创建工单
 */
export function createOrderReturn(par: {
  orderId: string
  email: string
  desc: string
  nextReturnAt: string
}) {
  return Axios.put('/web/orderReturn', par)
}

/**
 * @description: 获取回访订单列表
 */
export function getOrderReturnList(par: {
  orderId: string
  email: string
  status: string
  pages: number
  pageSize: number
}) {
  return Axios.get('/web/orderReturn', par)
}

/**
 * @description: 完成回访
 */
export function completeOrderReturn(par: {
  id: number
}) {
  return Axios.post('/web/orderReturn/complete', par)
}

/**
 * @description: 批量转移回访
 */
export function divertOrderReturn(par: {
  ids: number[]
  divertFrom: string
}) {
  return Axios.post('/web/orderReturn/divert', par)
}
