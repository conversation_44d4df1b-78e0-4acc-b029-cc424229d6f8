<!--
 * @Date         : 2021-04-19 17:49:35
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 * @LastEditTime : 2025-03-31 11:23:03
-->


<template>
  <router-view v-slot="{ Component }">
    <transition name="slide-left"
                appear
                mode="out-in">
      <keep-alive :include="keepAliveList">
        <component :is="Component" />
      </keep-alive>
    </transition>
  </router-view>

  <div class="oi-loading">
    <div class="oi-loading__wrap">
      <div class="oi-loading__cell oi-loading__egg1" />
      <div class="oi-loading__cell oi-loading__egg2" />
      <div class="oi-loading__cell oi-loading__egg3" />
      <div class="oi-loading__cell oi-loading__egg4" />
      <div class="oi-loading__cell oi-loading__egg5" />
      <div class="oi-loading__cell oi-loading__egg6" />
      <div class="oi-loading__cell oi-loading__egg7" />
      <div class="oi-loading__walk">
        <div class="oi-loading__people">
          <img class="oi-loading__hammer"
               src="https://fp.yangcong345.com/middle/loading/hammer.svg"
               alt="">
          <img class="oi-loading__body"
               src="https://fp.yangcong345.com/middle/loading/body.svg"
               alt="">
          <img class="oi-loading__cap"
               src="https://fp.yangcong345.com/middle/loading/cap.svg"
               alt="">
          <img class="oi-loading__head"
               src="https://fp.yangcong345.com/middle/loading/head.svg"
               alt="">
          <img class="oi-loading__arm"
               src="https://fp.yangcong345.com/middle/loading/arms.svg"
               alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import router from '@/router'

const keepAliveList = ref<string[]>([])
for (const item of router.options.routes) {
  if (item!.meta!.keepAlive && item!.name) {
    keepAliveList.value.push(item!.name as string)
  }
}
</script>

<style lang="scss">
body {
	margin: 0;
}

.slide-left-enter-from {
	transform: translateX( -20px);
	opacity: 0;
}

.slide-left-enter-to {
	transform: translateX(0px);
}

.slide-left-leave-from {
	transform: translateX(0);
}

.slide-left-leave-to {
	transform: translateX(20px);
	opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
	transition: all 0.3s;
}
</style>
