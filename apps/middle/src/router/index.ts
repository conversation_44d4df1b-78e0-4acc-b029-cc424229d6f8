import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/JumpTelsale',
    component: () => import('@/views/JumpTelSale/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/oAuth',
    component: () => import('@/views/oAuth/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/MessageTip',
    component: () => import('@/views/MessageTip/index.vue'),
    meta: {
      keepAlive: false
    }
  },
]

const router = createRouter({
  history: createWebHistory('kefu-web-pc/middle'),
  routes,
})

router.beforeEach(beforeEach)

export default router
