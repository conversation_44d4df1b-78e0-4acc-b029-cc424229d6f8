<!--
 * @Date         : 2023-12-26 14:42:51
 * @Description  : 跳转企微电销业务
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div />
</template>

<script lang='ts' setup>
import { browserGETMe, isInNative, getQueryObject } from '@guanghe-pub/onion-utils'
import { schoolYearToStage } from '#/script/stage'
import openUrl from '#/script/openUrl'
import { h5Url, getUrlMap } from './const/jumpUrl'
import { showConfirmDialog } from 'vant'
import 'vant/lib/index.css'
import 'vant/lib/dialog/index.css'

async function JumpTelsale() {
  const me:any = await browserGETMe()
  const userId = me.id
  const stage =  schoolYearToStage(me.schoolYear, me.schoolYear54or63 ?? '').label

  if (stage === '无') {
    open(h5Url, '_self')
  } else {
    let url = `${getUrlMap()[stage]}${userId}`
    openUrl(url, '_self')
  }
}

function check() {
  if (isInNative()) {
    showConfirmDialog({
      title: '洋葱学园想要打开“微信”',
    }).then(() => {
      JumpTelsale()
    }).catch(() => {
      console.log('用户取消了操作')
      history.back()
    })
  } else {
    open(h5Url, '_self')
  }
}

onMounted(() => {
  setTimeout(() => {  // 等待bridge加载
    check()
  }, 1000)
})

</script>

<style scoped lang='scss'>

</style>
