<!--
 * @Date         : 2024-12-20 18:00:03
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <iframe str
            :src="str"
            style="width: 100vw; height: 100vh"
            frameborder="0" />
  </div>
</template>

<script>
import { getQueryObject } from '@guanghe-pub/onion-utils'
import { showLoadingToast } from 'vant'

export default {
  name: 'OAuth',
  data() {
    return {
      code: '',
      str: '',
    }
  },
  methods: {
    auth() {
      showLoadingToast()
      const url =
        'https://telesale-work-wechat.yangcong345.com/api/qiwei/oauth2/proxy'
      const res = getQueryObject()
      const params = {
        callBack: res.callBack,
        currentID: res.currentID,
        code: res.code,
        accountId: res.accountId,
      }
      const idStr = params.accountId ? `&accountId=${params.accountId}` : ''
      this.str = `${url}?callBack=${params.callBack}&currentID=${params.currentID}&code=${params.code}${idStr}`
      console.log('str', this.str)
      console.log('params', params)
    },
  },
  created() {
    this.auth()
  },
}
</script>
