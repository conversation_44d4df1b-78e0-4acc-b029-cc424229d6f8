<!--
 * @Date         : 2024-12-20 18:31:26
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div id="app"
       class="d-box">
    <OIImgLoad v-if="state === '200'"
               class="d-img"
               src="https://fp.yangcong345.com/middle/1.0.0/telesale-c-web/images/messageTip_success-3cac738b92b3676da706f805a0202445__w.png"
               alt="操作成功" />
    <div v-else
         class="d-cont">
      <OIImgLoad class="d-img"
                 src="https://fp.yangcong345.com/middle/1.0.0/telesale-c-web/images/messageTip_fail-96e36e7ae5b70d3f5a9ea0381ad6ef09__w.png"
                 alt="操作成功" />
      <p v-if="message"
         class="d-msg">（失败原因：{{ message }}）</p>
    </div>
  </div>
</template>

<script>
import { OIImgLoad } from '@guanghe-pub/onion-ui'

export default {
  name: 'MessageTip',
  components: {
    OIImgLoad,
  },
  data() {
    return {
      state: '',
      message: '',
    }
  },
  created() {
    let strParams = decodeURI(window.location.search)
    if (strParams.indexOf('state=200') > -1) {
      this.state = '200'
    } else if (strParams.indexOf('message=') > -1) {
      let msg = strParams.split('message=')
      this.message = msg[1] || ''
    }
  },
}
</script>

<style lang="scss">
.d-box {
  height: 100vh;
}

.d-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.d-img {
  width: 300px;
  height: 300px;
}

.d-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80%;
}

.d-msg {
  font-size: 28px;
  color: #2b86db;
  word-break: break-all;
}
</style>
